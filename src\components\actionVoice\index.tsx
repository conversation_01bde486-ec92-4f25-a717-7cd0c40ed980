import IconCheckActive from '@/assets/check_active.svg'

import StorageEnvKey from '@/constants/StorageEnvKey'
import IconCheckDefault from '@/assets/check_default.svg'
import { Button, Cell, Image, Popup, RadioGroup } from '@antmjs/vantui'

import { Text, View } from '@tarojs/components'
import { pxTransform, getDeviceInfo } from '@tarojs/taro'
import classNames from 'classnames'
import { useEffect, useRef, useState } from 'react'

import { VoiceInteraction } from '@/constants/chatType'
import StorageUtils from '@/utils/StorageUtils'
import './index.less'
type Props = {
  show: boolean
  type: 'QUESTION_INTERACTION_TYPE' | 'SKILL_INTERACTION_TYPE' | 'INTELLIGENCE_INTERACTION_TYPE'
  onClose: () => void
  onConfirm: () => void
}

const Index: React.FC<Props> = (props) => {
  const { show, type, onClose, onConfirm } = props
  const [isIOS, setIsIOS] = useState(false)
  const [voiceInteractionType, setVoiceInteractionType] =
    useState<VoiceInteraction>()
  const voiceInteractionRef = useRef()
  const handleVoiceItemClick = (item: VoiceInteraction) => {
    setVoiceInteractionType(item)
  }
  const voicePopupCancel = () => {
    setVoiceInteractionType(voiceInteractionRef.current)
    onClose()
  }
  const voicePopupConfirm = () => {
    StorageUtils.set(StorageEnvKey[type],voiceInteractionType)
    onClose()
    onConfirm()
  }


  useEffect(() => {
    const isIOS = getDeviceInfo().platform === 'iOS'
    setIsIOS(isIOS)
  }, [])
  useEffect(() => {
    const interactionType = StorageUtils.get(StorageEnvKey[type]) || VoiceInteraction.Manual
    setVoiceInteractionType(
      interactionType
    )
  }, [show, type])

  return (
    <Popup
      position="bottom"
      show={show}
      closeable
      onClose={onClose}
      safeAreaInsetBottom
      round
      zIndex={110}
    >
      <View className="voicetype_popup_title">语音互动设置</View>
      <View className="voicetype_popup_tip">修改成功后，将于下次练习生效</View>
      <View className="voicetype_popup_list">
        <RadioGroup direction="vertical" value={voiceInteractionType}>
          <Cell
            border={false}
            renderTitle={
              <View>
                <View className="voicetype_popup_head">自动发送语音</View>
                <View className="voicetype_popup_subhead">
                  自动识别语音停顿，自动发送语音，此模式最长录音<Text className='m'>60s</Text>
                </View>
              </View>
            }
            renderIcon={
              <View className="voicetype_popup_checkbox">
                <Image
                  className={classNames('voicetype_popup_check', {
                    voicetype_popup_check_active:
                      voiceInteractionType === VoiceInteraction.Auto,
                  })}
                  src={IconCheckActive}
                />
                <Image
                  className={classNames('voicetype_popup_check', {
                    voicetype_popup_check_active:
                      voiceInteractionType === VoiceInteraction.Manual,
                  })}
                  src={IconCheckDefault}
                />
              </View>
            }
            key={VoiceInteraction.Auto}
            clickable
            onClick={() => handleVoiceItemClick(VoiceInteraction.Auto)}
          />
          <Cell
            border={false}
            renderTitle={
              <View>
                <View className="voicetype_popup_head">长按发送语音</View>
                <View className="voicetype_popup_subhead">
                  按住识别语音，松手发送语音
                </View>
              </View>
            }
            renderIcon={
              <View className="voicetype_popup_checkbox">
                <Image
                  className={classNames('voicetype_popup_check', {
                    voicetype_popup_check_active:
                      voiceInteractionType === VoiceInteraction.Manual,
                  })}
                  src={IconCheckActive}
                />
                <Image
                  className={classNames('voicetype_popup_check', {
                    voicetype_popup_check_active:
                      voiceInteractionType === VoiceInteraction.Auto,
                  })}
                  src={IconCheckDefault}
                />
              </View>
            }
            key={VoiceInteraction.Manual}
            clickable
            onClick={() => handleVoiceItemClick(VoiceInteraction.Manual)}
          />
        </RadioGroup>
      </View>
      <View className={classNames('voicetype_popup_actions', {
        'voicetype_popup_actions_safe': isIOS,

      })}>
        <Button
          onClick={voicePopupCancel}
          style={{
            '--padding-md': pxTransform(28),
            '--button-normal-height': pxTransform(96),
            color: '#777777',
          }}
          round
          block
          color="#F6F6F6"
        >
          取消
        </Button>
        <Button
          onClick={voicePopupConfirm}
          style={{
            '--padding-md': pxTransform(28),
            '--button-normal-height': pxTransform(96),
          }}
          round
          block
          color="linear-gradient(270deg, #6742FF 0.03%, #3D83FF 100.03%)"
        >
          确定
        </Button>
      </View>
    </Popup>
  )
}

export default Index
