
interface SpeechRecognizerFlashResult {
    request_id: string;
    data: string;
}
interface SpeechRecognizerFlashParams {
    appid: number;
    data: any;
    dataLen: number;
    secretid: string;
    secretkey: string;
    engine_type: string;
    voice_format: string;
    hotword_id?: string;
    customization_id?: string;
    input_sample_rate?: number;
    success?: (result: { request_id: string; result: string }) => void;
    fail?: (error: TaroGeneral.CallbackResult) => void;
}

interface SpeechRecognizerParams {
  appid: string;
  secretid: string;
  secretkey: string;
  engine_model_type: string;
  needvad?: number;
  voice_format?: number;
  filter_empty_result?: number;
  vad_silence_time?: number;
  max_speak_time?: number;
  noise_threshold?: number;
  input_sample_rate?: number;
  signCallback?: (queryStr: string) => string;
}
