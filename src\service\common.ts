import request from './requestTaro';
import { Response, TtsVoiceQuery } from '@/types/common';
export function getServerTime() {
  return request({
      url: `/common/server-time`
  }) as Response<string>;
}

export function ttsVoice(data: TtsVoiceQuery, signal: AbortSignal) {
  return request({
      url: '/common/tts-voice',
      method: 'GET',
      data,
      signal,
      timeout: 12000
  }) as Response<string>;
}

// 上传日志
export function uploadLog(log: string) {
  return request({
      url: '/common/miniapp-error-log',
      method: 'POST',
      data: {
        log
      }
  }) as Response<boolean>;
}
