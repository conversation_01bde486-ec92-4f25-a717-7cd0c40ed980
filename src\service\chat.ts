import { Response } from '@/types/common';
import { ChatVO, EmanVO, HistoryVO, ScriptVO } from '@/types/chat';
import request from './requestTaro';

export function getChat(id: string) {
  return request({
      url: `/chat/${id}`
  }) as Response<ChatVO>;
}


export function getScript(id: string) {
  return request({
      url: `/script/${id}`
  }) as Response<ScriptVO>;
}

export function getEnam(id: string) {
  return request({
      url: `/eman/${id}`
  }) as Response<EmanVO>;
}

export function doneChat(id: string, shareFlag: boolean) {
  return request({
      url: `/chat/${id}/done?shareFlag=${shareFlag}`,
      method: 'PUT'
  }) as Response<any>;
}

// 会话结束并生成报告
export function doneGenerateReport(id: string, shareFlag: boolean) {
  return request({
      url: `/chat/${id}/doneGenerateReport?shareFlag=${shareFlag}`,
      method: 'PUT'
  }) as Response<any>;
}


// 生成对话结束报告
export function generateReport(chatId: string, shareFlag: boolean) {
  return request({
      url: `/chat/report/${chatId}?shareFlag=${shareFlag}`,
      method: 'POST'
  }) as Response<any>;
}

export function qaCompletions(chatid: string, data: { qaId: string | number; answer: string, audioFileName: string }) {
  return request({
      url: `/chat/${chatid}/qaCompletions`,
      method: 'POST',
      data
  }) as Response<{ chatQAId: string; chatHistoryId: string }>;
}


export function qaAnswerRewrite(data: any) {
  return request({
      url: `/chat/qaAnswerRewrite`,
      method: 'POST',
      data
  }) as Response<string>;
}

export function qaSave(chatid: string, qaId: string | number) {
  return request({
      url: `/chat/${chatid}/qaSaveQuestion/${qaId}`,
      method: 'POST'
  }) as Response<boolean>;
}

export function getAllChatHistory(id: string) {
  return request({
      url: `/chat/${id}/history/all`
  }) as Response<HistoryVO[]>;
}


export function clearContext(chatid: string) {
  return request({
      url: `/chat/${chatid}/clear-context`,
      method: 'POST'
  }) as Response<boolean>;
}

// 保存开场白
export function saveIntroduction(chatId: string) {
  return request({
      url: `/chat/${chatId}/saveIntroduction`,
      method: 'POST'
  }) as Response<boolean>;
}
