import { Button, Popup, Space } from "@antmjs/vantui"
import { View } from "@tarojs/components"
import { pxTransform,getDeviceInfo } from "@tarojs/taro"
import '../actionVoice/index.less'
import { useEffect, useState } from "react"
import classNames from "classnames"

type Props = {
  show: boolean
  onClose: () => void
  onConfirm: () => void
}

const Index: React.FC<Props> = (props) => {
  const { show, onClose, onConfirm } = props
  const [isIOS, setIsIOS] = useState(false)

  useEffect(() => {
    const isIOS = getDeviceInfo().platform === 'iOS'
    setIsIOS(isIOS)
  }, [])
  return (<Popup
    position="bottom"
    show={show}
    closeable={false}
    onClose={onClose}
    closeOnClickOverlay={false}
    safeAreaInsetBottom
    round
    zIndex={110}
  >
    <View className="voicetype_popup_title">开启声音对话</View>
    <View className="voicetype_popup_tip">为了您与本次对话时的体验完整，请打开声音</View>
    <Space />
    <View className={classNames('voicetype_popup_actions', {
      'voicetype_popup_actions_safe': isIOS,

    })}>
        {/* <Button
          onClick={onClose}
          style={{
            '--padding-md': pxTransform(28),
            '--button-normal-height': pxTransform(96),
            color: '#777777',
          }}
          round
          block
          color="#F6F6F6"
        >
          静音体验
        </Button> */}
        <Button
          onClick={onConfirm}
          style={{
            '--padding-md': pxTransform(28),
            '--button-normal-height': pxTransform(96),
          }}
          round
          block
          color="linear-gradient(270deg, #6742FF 0.03%, #3D83FF 100.03%)"
        >
          打开声音
        </Button>
      </View>
  </Popup>)
}

export default Index
