import { RoleEnum } from "@/constants/chatType";
import { CosConfig } from "@/service/cos";

export interface EmanChatHistoryVo {
  chatId: number;
  chatReportId: number;
  scriptName: string;
  createTime: string;
  finishTime: string;
  score: number;
  status: number;
}
export interface EmanVO {
  avatar: string;
  background: string;
  createTime: string;
  department: string;
  id: string;
  name: string;
  occupation: string;
  organization: string;
  personality: string;
  title: string;
  tone: string;
  skill: string;
  updateTime: string;
  chatList: EmanChatHistoryVo[];
  status: number;
  type: number; // 1-情景演练 2-企业智脑
  gender: number;
  scene: {
      id: string;
      scene: string;
  };
  zipFileUrl?: string;
  introduction: string;
  introductionDelay: number
  introductionType:number
}

export interface Question {
  id: number;
  question: string;
}
export interface ScriptVO {
  backdrop: string;
  createTime: string;
  deleted: boolean;
  goal: string;
  id: string;
  location: string;
  name: string;
  product: string;
  time: string;
  timeLimit: number;
  trust: number;
  updateTime: string;
  randomFlag: number; // 0-按顺序提问 1-随机提问顺序 2-随机指定数量题目 3-随机部分比例题目
  randomNum: number;
  type: number; // 脚本类型 1-技巧 2-问答
  question: Question[];
  applicableObject: string;
  applicable: boolean; // 是否适用e人
  visitObject: string; // 拜访对象
  department: string;
  lockFlag: boolean;
  disableTextInputFlag: boolean;
}

export interface SceneVO {
  id: string;
  scene: string;
}

export interface QaItem {
  qaId: string;
  answer: string;
  question: string;
  chatQAId?: string;
  chatHistoryId?: string;
  ossConfig?: CosConfig;
}
export interface ChatVO {
  chatReportId: string;
  createTime: string;
  deleted: boolean;
  done: boolean;
  emanId: string;
  eman: EmanVO;
  qaList: QaItem[];
  id: string;
  scene: SceneVO;
  sceneId: string;
  script: ScriptVO;
  scriptId: string;
  updateTime: string;
  userId: string;
  type: number;
}

export interface HistoryVO {
  role: RoleEnum;
  content: string;
  avatar?: string;
  id?: string;
  createTime?: string;
  qaId?: string;
  audioFlag?: boolean;
  audioUrl?: string;
  upload?: boolean;
}
