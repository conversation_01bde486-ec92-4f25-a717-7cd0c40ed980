
import { useDidShow } from '@tarojs/taro';
import { useMount } from 'ahooks';
import { useRef } from 'react';

/**
 * 组件首次渲染和组件切换显示的时候执行的 hook
 *
 * @description 在 子组件中调用 useLoad 或者 useDidShow 的时候会存在不执行的情况，所以需要使用 useMount 做兜底，但 useDidShow 和 useMount 相冲突，所以内部进行处理，首次的时候在 useMount中执行，非首次的情况在 useDidShow 中执行
 * @param callback 回调函数
 * @param mount 是否只首次执行
 */
export function useShow(callback: () => void, mount?: boolean) {
    const mountRef = useRef(true);

    useMount(() => {
        callback();
        mountRef.current = false;
    });

    useDidShow(() => {
        if (!mount && mountRef.current === false) {
            callback();
        }
    });
}
