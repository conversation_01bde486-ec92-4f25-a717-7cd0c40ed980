const  biz = 2

const serverTest = 'https://yb.ecaiabc.com/api/v1'
const serverRelease = 'https://www.hiyubei.com/prod-api/api/v1'

const QCloudAIVoiceTest = {
  appId: **********,
  secretId: 'AKIDaI6Oj6Sp8Mg1Hbq6eBgAWtN9V2hQZo9e',
  secretKey: 'qcRWdjjxKoJh7TnIwY1XzNj2JgBmXRSP',
  hotword_id: '17994de945c311ef8484525400aec391',
  customization_id: 'd75bcfcb45d811ef8484525400aec391'
}

const QCloudAIVoiceRelease = {
  appId: **********,
  secretId: 'AKID3W6f6KL8h3B50rTtCJSN4XourcCZjjZC',
  secretKey: 'XqvmPo13whFYcSPXlupWzEI7i5GDwRil',
  hotword_id: '591d5ad1451611ef8484525400aec391',
  customization_id: 'd75bcfcb45d811ef8484525400aec391'
}

const LICENSE_KEY = 'xuniren-20241111_0234'
const SHUZIREN_TEST = {
  LICENSE_URL: `http://localhost:8080/shuziren/${LICENSE_KEY}.license`,
  LICENSE_KEY
}

const SHUZIREN_TRIAL = {
  LICENSE_URL: `https://yb.ecaiabc.com/shuziren/${LICENSE_KEY}.license`,
  LICENSE_KEY

}
const SHUZIREN_RELEASE = {
  LICENSE_URL: `https://www.hiyubei.com/shuziren/${LICENSE_KEY}.license`,
  LICENSE_KEY
}


console.log(window.location)

const envConfigs: {
  [key: string]: CONFIG
} = {
  develop: {
    biz,
    QCloudAIVoice: QCloudAIVoiceTest,
    server: '',
    shuziren: SHUZIREN_TEST
  },
  trial: {
    biz,
    QCloudAIVoice: QCloudAIVoiceTest,
    server: serverTest,
    shuziren: SHUZIREN_TRIAL
  },
  release: {
    biz,
    QCloudAIVoice: QCloudAIVoiceRelease,
    server: serverRelease,
    shuziren: SHUZIREN_RELEASE
  }
}
console.log(process.env.TARO_APP_MODE)


export default envConfigs[process.env.TARO_APP_MODE]
