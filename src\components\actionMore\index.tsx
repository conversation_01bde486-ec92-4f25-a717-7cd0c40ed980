import IconReport from '@/assets/icon_report.svg'
import IconVoice from '@/assets/icon_voice.svg'
import IconCleanBlack from '@/assets/icon_clean_black.svg'
import IconFeedback from '@/assets/icon_feedback.svg'
import { ActionSheet, Image } from '@antmjs/vantui'
import { View } from '@tarojs/components'
import { getDeviceInfo } from '@tarojs/taro'
import './index.less'
import { useEffect, useState } from 'react'
import classNames from 'classnames'
type Props = {
  show: boolean
  showCleanContext: boolean
  onClose: () => void
  onScript: () => void
  onSetting: () => void
  onCleanContext: () => void
  onFeedback: () => void
}

const Index: React.FC<Props> = (props) => {
  const {
    show,
    showCleanContext,
    onClose,
    onScript,
    onSetting,
    onCleanContext,
    onFeedback
  } = props
  const [isIOS, setIsIOS] = useState(false)

  useEffect(() => {
    const platform = getDeviceInfo().platform
    console.log('platform', platform)
    setIsIOS(platform === 'iOS')
  }, [])
  return (
    <ActionSheet
      className="action_more"
      zIndex={110}
      show={show}
      onClose={onClose}
      closeOnClickOverlay={false}
      style={{
        // @ts-ignore
        '--action-sheet-close-icon-color': '#272C47',
      }}
      title="更多"
    >
      <View
        className={classNames('action_more_content', {
          action_more_content_safe: isIOS,
        })}
      >
        {showCleanContext ? (
          <View className="action_more_item" onClick={onCleanContext}>
            <Image src={IconCleanBlack} className="action_more_item_icon" />
            <View className="action_more_item_text">清除上下文</View>
          </View>
        ) : (
          <View className="action_more_item" onClick={onScript}>
            <Image src={IconReport} className="action_more_item_icon" />
            <View className="action_more_item_text">查看脚本</View>
          </View>
        )}
        <View className="action_more_item" onClick={onSetting}>
          <Image src={IconVoice} className="action_more_item_icon" />
          <View className="action_more_item_text">语音交互设置</View>
        </View>
        <View className="action_more_item" onClick={onFeedback}>
          <Image src={IconFeedback} className="action_more_item_icon" />
          <View className="action_more_item_text">问题反馈</View>
        </View>
      </View>
    </ActionSheet>
  )
}

export default Index
