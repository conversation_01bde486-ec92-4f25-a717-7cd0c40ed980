const envVersion = process.env.TARO_APP_MODE

export default  {
  TOKEN: `${envVersion}_TOKEN`,
  TENANT_ID: `${envVersion}_TENANT_ID`,
  APP_ID: `${envVersion}_APP_ID`,
  IS_ENTERPRISE: `${envVersion}_IS_ENTERPRISE`,
  CHAT_MODE: `${envVersion}_CHAT_MODE`,
  QUESTION_INTERACTION_TYPE: `${envVersion}_QUESTION_INTERACTION_TYPE`,
  SKILL_INTERACTION_TYPE: `${envVersion}_SKILL_INTERACTION_TYPE`,
  INTELLIGENCE_INTERACTION_TYPE: `${envVersion}_INTELLIGENCE_INTERACTION_TYPE`,
  SUBTITLE_VISIBLE: `${envVersion}_SUBTITLE_VISIBLE`
}
