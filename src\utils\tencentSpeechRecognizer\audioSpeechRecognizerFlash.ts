import { arrayBufferToBase64, request } from '@tarojs/taro';
import { HmacSHA1 } from 'crypto-js';

function formatSignString(query: any, params: any): string {
    let strParam: string = '';
    const signStr: string = `asr.cloud.tencent.com/asr/flash/v1/${query.appid}`;
    // if (query.appid) {
    //     signStr += query.appid;
    // }
    const keys: string[] = Object.keys(params);
    keys.sort();
    for (let i = 0; i < keys.length; i++) {
        strParam += `&${keys[i]}=${params[keys[i]]}`;
    }
    return `${signStr}?${strParam.slice(1)}`;
}
function createQuery(query: SpeechRecognizerFlashParams) {
    const params: any = {};
    const time: number = new Date().getTime();
    params.secretid = query.secretid || '';
    params.engine_type = query.engine_type || '16k_zh_large';
    params.timestamp = Math.round(time / 1000);
    params.voice_format = query.voice_format || 'mp3';
    params.hotword_id = query.hotword_id || '';
    params.customization_id = query.customization_id || '';
    params.input_sample_rate = query.input_sample_rate || 8000;
    return params;
}
function toUint8Array(wordArray: any): Uint8Array {
    // Shortcuts
    const { words } = wordArray;
    const { sigBytes } = wordArray;

    // Convert
    const u8: Uint8Array = new Uint8Array(sigBytes);
    for (let i = 0; i < sigBytes; i++) {
        u8[i] = (words[i >>> 2] >>> (24 - (i % 4) * 8)) & 0xff;
    }
    return u8;
}

function signCallback(secretKey: string, signStr: string): string {
    const hash = HmacSHA1(signStr, secretKey);
    const bytes = toUint8Array(hash);
    return arrayBufferToBase64(bytes);
}

function getUrl(params: SpeechRecognizerFlashParams): string {
    if (!params.appid || !params.secretid) {
        return '';
    }
    try {
        const urlQuery: any = createQuery(params);
        const queryStr: string = formatSignString(params, urlQuery);

        return queryStr;
    } catch (error) {
        console.log(error);
        return '';
    }
}

const audioSpeechRecognizerFlash = (params: SpeechRecognizerFlashParams) => {
    console.log('audioSpeechRecognizerFlash params', params);
    const queryStr: string = getUrl(params);
    console.log('audioSpeechRecognizerFlash queryStr', queryStr);

    const signature: string = signCallback(params.secretkey, `POST${queryStr}`);
    request({
        url: `https://${getUrl(params)}`,
        method: 'POST',
        data: params.data,
        header: {
            Host: 'asr.cloud.tencent.com',
            Authorization: signature,
            'Content-Type': 'application/octet-stream',
            'Content-Length': params.dataLen
        },
        success: (res: any) => {
            console.log('audioSpeechRecognizerFlash success', res);
            if (res.statusCode === 200) {
                if (res.data.code === 0) {
                    if (params.success) {
                        if (res.data.flash_result && res.data.flash_result.length > 0) {
                            params.success({ request_id: res.data.request_id, result: res.data.flash_result[0].text });
                        } else {
                            params.success({ request_id: res.data.request_id, result: '' });
                        }
                    }
                } else {
                    if (params.fail) {
                        params.fail(res);
                    }
                }
            } else {
                if (params.fail) {
                    params.fail(res);
                }
            }
        },
        fail: (error: TaroGeneral.CallbackResult) => {
            if (params.fail) {
                params.fail(error);
            }
        }
    });
};

export default audioSpeechRecognizerFlash;
