
import { useRequest as useRequestA } from 'ahooks';
import { useShow } from '../useShow';

import type { Response } from '@/types/common';
// import Taro from '@tarojs/taro';

import frontLogout from '@/utils/fontLogout';
import Taro from '@tarojs/taro';
import type { Options as AOptions, Plugin } from 'ahooks/es/useRequest/src/types';
import { useRef } from 'react';
import wx from 'weixin-js-sdk';

type Service<TData, TParams extends any[]> = (...args: TParams) => Response<TData>;

interface Options<TData, TParams extends any[]> extends AOptions<TData, TParams> {
    /** DidShow 页面显示的时候是否刷新 默认 true */
    showRefresh?: boolean;
}

export function useRequest<TData, TParams extends any[] = any[]>(
    service: Service<TData, TParams>,
    options?: Options<TData, TParams>,
    plugins?: Plugin<TData, TParams>[]
) {
    const { showRefresh, ...resetOptions } = options || {};

    const loadingDelayRef = useRef(0);

    const _options = {
        // refreshShow show 刷新的时候，默认 延迟200毫秒，避免闪烁
        loadingDelay: showRefresh ? loadingDelayRef.current : undefined,
        ...resetOptions,
        manual: true
    };

    const resetService = async (...args: any) => {
        const { data } = await service(...args);

        if (data.code === 401 || data.code === 20004) {
            Taro.showToast({
                icon: 'none',
                title: '登录信息失效，请重新登录',
                mask: true
            });

            frontLogout();
            setTimeout(async () => {
              wx.miniProgram.redirectTo({
                url: '/pages/login/index/index'
              });
            }, 1500);
        } else if (data.code === 200) {
            return data.data;
        } else {
            Taro.showToast({
                icon: 'none',
                title: data.message
            });

            return null;
        }
    };

    // @ts-ignore
    const response = useRequestA(resetService, _options, plugins);

    useShow(() => {
        if (!resetOptions?.manual) {
            // @ts-ignore
            response.run();
            loadingDelayRef.current = 200;
        }
    }, !showRefresh);

    return response;
}
