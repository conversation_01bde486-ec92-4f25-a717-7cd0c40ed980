import { Button, Form, FormItem, Popup } from '@antmjs/vantui'
import { Textarea, View } from '@tarojs/components'
import { pxTransform } from '@tarojs/taro'
import './index.less'
import { useState } from 'react'
interface Props {
  show: boolean
  onClose: () => void
  onConfirm: (values: {description: string}) => void
}

const Index: React.FC<Props> = (props) => {
  const { show, onClose, onConfirm } = props
  const [length, setLength] = useState(0)
  const formIt = Form.useForm()
  const ruleContent = {
    rule: (values: string, callback: (errMess: string) => void) => {
      if (values.length > 200) {
        callback(`文字太多啦${values.length}/200`)
      }
    },
  }

  const handleClose = () => {
    formIt.resetFields()
    setLength(0)
    onClose()
  }

  const onChange = (e: any) => {
    setLength(e.detail.value.length)
  }

  const handleSubmit = () => {
    formIt.validateFields(async (errorMessage, fieldValues: any) => {
      console.log(fieldValues,fieldValues)
      if (errorMessage && errorMessage.length) {
        return console.info('errorMessage', errorMessage)
      }
      onConfirm({...fieldValues})
      handleClose()
    })
  }

  return (
    <Popup className="popup_feedback" zIndex={110} show={show} round  closeOnClickOverlay={false} onClose={handleClose}>
      <Form form={formIt}>
        <FormItem
          name="description"
          feedback='hidden'
          required
          layout="vertical"
          label="问题描述"
          trigger='onInput'
          valueFormat={(e) => e.detail.value}
          rules={ruleContent}
        >
          <Textarea
            className="textarea"
            maxlength={200}
            onInput={onChange}
            placeholder="请描述一下您遇到的问题，以便我们提供更好地服务。"
          />

        </FormItem>
        <View className='limit'>
          {length}/200
        </View>
      </Form>
      <View className="action_btns">
        <Button
          onClick={handleClose}
          style={{
            '--padding-md': pxTransform(26),
            '--button-normal-height': pxTransform(86),
            color: '#777777',
          }}
          round
          block
          color="#F6F6F6"
        >
          取消
        </Button>
        <Button
          onClick={handleSubmit}
          disabled={length === 0}
          style={{
            '--padding-md': pxTransform(26),
            '--button-normal-height': pxTransform(86),
          }}
          round
          block
          color="linear-gradient(270deg, #6742FF 0.03%, #3D83FF 100.03%)"
        >
          确定
        </Button>
      </View>
    </Popup>
  )
}

export default Index
