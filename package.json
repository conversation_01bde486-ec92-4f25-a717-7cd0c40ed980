{"name": "quantun-digitalman", "version": "1.0.0", "private": true, "description": "数字人", "templateInfo": {"name": "default", "typescript": true, "css": "Less", "framework": "React"}, "scripts": {"build:h5": "taro build --type h5", "build:h5-trial": "taro build --type h5 --mode trial", "dev:h5": "npm run build:h5 -- --watch", "test": "jest"}, "author": "", "dependencies": {"@antmjs/vantui": "^3.5.0", "@babel/runtime": "^7.21.5", "@bddh/starling-dh-picture-client": "1.0.36", "@microsoft/fetch-event-source": "^2.0.1", "@tarojs/components": "3.6.32", "@tarojs/helper": "3.6.32", "@tarojs/plugin-framework-react": "3.6.32", "@tarojs/plugin-platform-alipay": "3.6.32", "@tarojs/plugin-platform-h5": "3.6.32", "@tarojs/plugin-platform-harmony-hybrid": "3.6.32", "@tarojs/plugin-platform-jd": "3.6.32", "@tarojs/plugin-platform-qq": "3.6.32", "@tarojs/plugin-platform-swan": "3.6.32", "@tarojs/plugin-platform-tt": "3.6.32", "@tarojs/plugin-platform-weapp": "3.6.32", "@tarojs/react": "3.6.32", "@tarojs/runtime": "3.6.32", "@tarojs/shared": "3.6.32", "@tarojs/taro": "3.6.32", "ahooks": "^3.8.1", "classnames": "^2.5.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "lodash": "^4.17.21", "react": "^18.0.0", "react-dom": "^18.0.0", "taro-lottie": "^1.0.2", "weixin-js-sdk": "^1.6.5"}, "devDependencies": {"@babel/core": "^7.8.0", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.5", "@tarojs/cli": "3.6.32", "@tarojs/plugin-html": "^3.6.32", "@tarojs/taro-loader": "3.6.32", "@tarojs/test-utils-react": "^0.1.1", "@tarojs/webpack5-runner": "3.6.32", "@types/crypto-js": "^4.2.2", "@types/jest": "^29.3.1", "@types/lodash": "^4.17.10", "@types/node": "^18.15.11", "@types/react": "^18.0.0", "@types/webpack-env": "^1.13.6", "@typescript-eslint/eslint-plugin": "^6.2.0", "@typescript-eslint/parser": "^6.2.0", "babel-plugin-import": "^1.13.8", "babel-preset-taro": "3.6.32", "eslint": "^8.12.0", "eslint-config-taro": "3.6.32", "eslint-plugin-import": "^2.12.0", "eslint-plugin-react": "^7.8.2", "eslint-plugin-react-hooks": "^4.2.0", "jest": "^29.3.1", "jest-environment-jsdom": "^29.5.0", "postcss": "^8.4.18", "prettier": "^3.3.3", "react-refresh": "^0.11.0", "stylelint": "^14.4.0", "ts-node": "^10.9.1", "tsconfig-paths-webpack-plugin": "^4.1.0", "typescript": "^5.1.0", "webpack": "5.95.0"}}