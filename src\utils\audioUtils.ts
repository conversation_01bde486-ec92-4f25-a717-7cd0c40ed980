
export const calculatePcmDuration = (data: ArrayBuffer, sampleRate: number = 16000, sampleDepth: number = 16) => {
  const bytesPerSample = sampleDepth / 8; // 每个样本的字节数
  const totalSamples = data.byteLength / bytesPerSample; // 总样本数
  const duration = totalSamples / sampleRate; // 时长（秒）
  return duration;
}

export const mp3Base64ToUrl = (base64AudioData: string) => {
  const audioData = base64AudioData.split(',')[1];
  // 将Base64数据转换为Blob对象（Blob可以作为音频资源）
  const blob = new Blob([Buffer.from(audioData, 'base64')], { type: 'audio/mpeg' });
  // 创建一个URL对象，指向这个Blob对象，使其可以作为音频源
  return URL.createObjectURL(blob);
}
