

import Taro from '@tarojs/taro';
import { isNil } from 'lodash';
import  StorageEnvKey from '@/constants/StorageEnvKey';
import { Logger } from './Logger';

export default class StorageUtils {
    static logger = new Logger('Storage');

    /**
     * 写入
     * @param key
     * @param value
     * @param validTime 有效时间，单位 ms
     */
    static set(key: keyof typeof StorageEnvKey | string, value: any, validTime?: number) {
        if (isNil(value)) {
            this.del(key);
            return;
        }
        Taro.setStorageSync(
            key,
            JSON.stringify({
                value,
                validTime,
                writeTime: Date.now()
            })
        );
    }
    static get(key: keyof typeof StorageEnvKey | string) {
        const result = Taro.getStorageSync(key);
        if (isNil(result) || result === '') {
            return null;
        }
        try {
            const { value, validTime, writeTime } = JSON.parse(result);
            if (validTime) {
                if (Date.now() - writeTime > validTime) {
                    // 已过期
                    this.del(key);
                    return null;
                }
            }
            return value;
        } catch (err) {
            this.logger.error(`获取 ${key} 失败`, err);
            // 清除掉不合法数据，防止持续报错
            this.del(key);
            return null;
        }
    }
    static del(key: keyof typeof StorageEnvKey | string) {
        Taro.removeStorageSync(key);
    }

    static clear() {
        Taro.clearStorageSync();
    }
}
