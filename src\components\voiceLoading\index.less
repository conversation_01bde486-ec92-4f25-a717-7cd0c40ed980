.loading_box {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 44px;
  &.barAnimate, &.barStatic {
    width: 300px;
    gap: 8px;
  }


  &.dot {
    width: 140px;
    gap: 20px;
  }

  &.shortBar {
    width: 80px;
    gap: 10px;
  }
}

.loading_dot {
  width: 20px;
  height: 20px;
  border-radius: 20px;
  background-color: rgba(255,255,255);
  opacity: 0.3;
  animation: dot 2s infinite;

  &:nth-child(1) {
    animation-delay: 1.5s;
  }
  &:nth-child(2) {
    animation-delay: 1s;
  }
  &:nth-child(3) {
    animation-delay: 0.5s;
  }
  &:nth-child(4) {
    animation-delay: 0s;
  }
}

@keyframes dot {
  0% {
    opacity: 0.3;
  }
  25% {
    opacity: 1;
  }
  50% {
    opacity: 0.3;
  }
  1% {
    opacity: 0.3;
  }
}

.loading-bar-mixin (@n, @i: 20) when (@i > 0) {
  &:nth-child(@{i}) {
    animation-delay: (@n - @i) * 0.3s;
  }
  .loading-bar-mixin(@n, @i - 1);
}

.loading_bar_static {
  width: 8px;
  height: 15px;
  background-color: #fff;
  border-radius: 8px;
}
.loading_bar_animate {
  .loading_bar_static();

  .loading-bar-mixin(20, 20);
  animation: bar 1.5s infinite;
  animation-play-state: running

}
// .loading_bar_box {
//   height: 44px;
//   display: flex;
//   align-items: center;
//   justify-content: center;
//   gap: 8px;

// }
@keyframes bar {
  0% {
    height: 15px;
  }
  50% {
    height: 44px;
  }
  100% {
    height: 15px;
  }
}
.loading_shortBar {
   width: 8px;
    height: 44px;
    background-color: #fff;
    border-radius: 8px;
    animation: shortBar 1s infinite;

  .loading-bar-mixin(0, 5);
}

// .loading_shotBar_box {
//   height: 44px;
//   display: flex;
//   align-items: center;
//   justify-content: center;
//   gap: 8px;
// }

@keyframes shortBar {
  0% {
    height: 15px;
  }
  50% {
    height: 44px;
  }
  100% {
    height: 15px;
  }
}
