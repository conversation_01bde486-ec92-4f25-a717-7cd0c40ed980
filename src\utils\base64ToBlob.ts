/**
  * desc: base64对象转blob文件对象
  * @param base64  ：数据的base64对象
  * @param fileType  ：文件类型 mp3等;
  * @returns {Blob}：Blob文件对象
  */
export default function base64ToBlob(base64: string, fileType: string) {
  let typeHeader = 'data:audio/' + fileType + ';base64,'; // 定义base64 头部文件类型
  let audioSrc = typeHeader + base64; // 拼接最终的base64
  let arr = audioSrc.split(',');
  let array = arr[0].match(/:(.*?);/);
  console.log(array)
  let mime = (array && array.length > 1 ? array[1] : 'audio/mp3') || 'audio/mp3';
  // 去掉url的头，并转化为byte
  let bytes = window.atob(arr[1]);
  // 处理异常,将ascii码小于0的转换为大于0
  let ab = new ArrayBuffer(bytes.length);
  // 生成视图（直接针对内存）：8位无符号整数，长度1个字节
  let ia = new Uint8Array(ab);
  for (let i = 0; i < bytes.length; i++) {
    ia[i] = bytes.charCodeAt(i);
  }
  return new Blob([ab], {
    type: mime
  });
}
