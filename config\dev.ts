import type { UserConfigExport } from "@tarojs/cli";

export default {
   logger: {
    quiet: false,
    stats: true
  },
  mini: {},
  h5: {
    devServer: {
      port: 8080,
      open: false,
      proxy: {
        [process.env.TARO_APP_BASE_API]: {
          target: 'https://yb.ecaiabc.com/api/v1',
          // target: 'http://172.16.8.188:7002/api/v1',
          pathRewrite: { ['^'+process.env.TARO_APP_BASE_API]: '' },
          changeOrigin: true
        }
      }
    },
  }
} satisfies UserConfigExport
