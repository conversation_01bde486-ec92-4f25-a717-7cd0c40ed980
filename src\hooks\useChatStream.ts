import StorageEnvKey from '@/constants/StorageEnvKey'
import { useState } from 'react'
import { useStorage } from './useStorage'
import { useEvent } from '@/utils/react-utils'
// import Taro from '@tarojs/taro';
import config from '@/config'
import { fetchEventSource } from '@microsoft/fetch-event-source'

/** 对话流处理 */
export function useChatStream(options: {
  ctrl: AbortController
  onProcess: (content: string) => void
  onEnd: () => void
  onError?: (error: any) => void
}) {
  const { ctrl, onEnd, onProcess, onError } = options
  const [token] = useStorage<string>(StorageEnvKey.TOKEN)
  const [tenantId] = useStorage<string>(StorageEnvKey.TENANT_ID)
  const [isEnterprise] = useStorage<number>(StorageEnvKey.IS_ENTERPRISE)
  const [appid] = useStorage<string>(StorageEnvKey.APP_ID)
  const [loading, setLoading] = useState(false)

  const run = useEvent(
    (options: { chatId: string; message: string; audioFileName: string }) => {
      const { chatId, message, audioFileName } = options

      setLoading(true)
      const header: any = {
        'Content-Type': 'application/json',
        Authorization: token,
      }
      if (tenantId) {
        header.tenantId = tenantId
      }
      if (isEnterprise === 0) {
        header.appid = appid
      }
      const url = config.server + process.env.TARO_APP_BASE_API
      console.log('useChatStream', url)

      fetchEventSource(
        config.server +
          process.env.TARO_APP_BASE_API +
          `/chat/${chatId}/completions`,
        {
          method: 'POST',
          headers: header,
          signal: ctrl.signal,
          body: JSON.stringify({
            input: message,
            audioFileName: audioFileName || null,
            type: 1, // 响应类型，1-sseEmitter， 2-responseBodyEmitter， 3-http轮询获取
          }),
          onopen: (res) => {
            console.log('onopen', res)
          },
          onerror(err) {
            console.log('onerror', err)
            ctrl.abort()

            setLoading(false)
            onError?.(err)
          },
          onmessage: (res) => {
            console.log('onmessage', res)
            const text = res.data
            if (res.event == 'event_complete') {
              // ctrl.abort();
              console.log('onEnd()', text)
              setLoading(false)

              onEnd()
            } else if (res.event === 'event_error') {
              ctrl.abort()
              console.log('onEnd()', text)
              setLoading(false)

              onError?.(res.data)
            } else {
              if (text.includes('"code":500')) {
                console.log('500', text)
                setLoading(false)
                onError?.({errMsg: text})
              } else if (text.includes('"code":401')) {
                console.log('401', text)
                setLoading(false)
                onError?.({errMsg: text})
              } else if (text.includes('当前人数使用过多')) {
                setLoading(false);
                onError?.({ errMsg: 'timeout' });
              } else {
                onProcess(text)
              }
            }
          },
        },
      )
    },
  )
  return { loading, run }
}
