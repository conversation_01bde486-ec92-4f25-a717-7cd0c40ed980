export interface QueryPagination {
  /** 当前页 */
  pageNo?: number;
  /** 每页数量 */
  pageSize?: number;
  /** 模糊查询 */
  keyword?: number;
}

/** 请求响应 */
export type Response<T = void> = Promise<
  {
      data: {
          code: number;
          data: T;
          message: string;
      };
  } & Record<string, any>
>;

/** 分页响应数据 */
export type Pagination<T = any> = {
  size: number;
  current: number;
  pages: number;
  records: T[];
  total: number;
};
/** 分页请求响应 */
export type PagingResponse<T = any> = Response<Pagination<T>>;

export interface TtsVoiceQuery {
  name: string;
  type: number;
  text: string;
  encoding: 'mp3' | 'pcm';
}


export interface LogInfo {
  level: 'error' | 'warn' | 'info' | 'debug' | 'trace';
  message: string;
  data?: any;
  time?: string;
}

export interface FeedbackData {
  appId: string;
  path: string;
  client: 'h5' | 'wechat';
  chatId: string;
  description: string;
  logs: LogInfo[];
}
