
import { Response } from '@/types/common'
import request from './requestTaro'
import Taro from '@tarojs/taro'

export type CosConfig = {
  audioFileName: string
  // starttime: string
  // expiredtime: string
  path: string
  // sessiontoken: string
  // tmpsecretid: string
  // tmpsecretkey: string
  // region: string
  // bucket: string
  uploadUrl: string;
  fileData?: [];
  mimeType: string;
}

// export function cosUpload(params: CosConfig, onProgress?: (progressData: any) => void) {
//   console.log('cosupload', params)
//   const cos = new COS({
//     SecretId: params.tmpsecretid, // sts服务下发的临时 secretId
//     SecretKey: params.tmpsecretkey, // sts服务下发的临时 secretKey
//     SecurityToken: params.sessiontoken, // sts服务下发的临时 SessionToken
//     StartTime: Number(params.starttime), // 建议传入服务端时间，可避免客户端时间不准导致的签名错误
//     ExpiredTime: Number(params.expiredtime), // 临时密钥过期时间
//   })
//   const arrayBuffer = new ArrayBuffer(params.fileData.length);
//   const uint8Array = new Uint8Array(arrayBuffer)
//   uint8Array.set(params.fileData)
//   const file = new File([arrayBuffer], params.audioFileName+'.pcm')
//   return cos.uploadFile({
//     Bucket: params.bucket /* 填写自己的 bucket，必须字段 */,
//     Region: params.region /* 存储桶所在地域，必须字段 */,
//     Key: params.path /* 存储在桶里的对象键（例如:1.jpg，a/b/test.txt，图片.jpg）支持中文，必须字段 */,
//     Body: file, // 上传文件对象
//     SliceSize:
//       1024 *
//       1024 *
//       10 /* 触发分块上传的阈值，超过5MB使用分块上传，小于5MB使用简单上传。可自行设置，非必须 */,
//     onProgress: function (progressData) {
//       console.log('progress',JSON.stringify(progressData))
//       onProgress && onProgress(progressData)
//     },
//   })
// }

// export function cosDownload(params: CosConfig) {
//   const cos = new COS({
//     SecretId: params.tmpsecretid, // sts服务下发的临时 secretId
//     SecretKey: params.tmpsecretkey, // sts服务下发的临时 secretKey
//     SecurityToken: params.sessiontoken, // sts服务下发的临时 SessionToken
//     StartTime: Number(params.starttime), // 建议传入服务端时间，可避免客户端时间不准导致的签名错误
//     ExpiredTime: Number(params.expiredtime), // 临时密钥过期时间
//   })

//   return cos.getObject({
//     Bucket: params.bucket /* 填写自己的 bucket，必须字段 */,
//     Region: params.region /* 存储桶所在地域，必须字段 */,
//     Key: params.path,
//   })
// }

export function audioUploadComplete(key: string) {
  return request({
    url: `/chat/audioUploadComplete`,
    method: 'POST',
    data: {
      key,
    },
  }) as Response<boolean>
}

export function cosUpload(cosUpload: CosConfig, data: any) {
  return new Promise((resolve, reject) => {
    const arrayBuffer = new ArrayBuffer(data.length);
    const uint8Array = new Uint8Array(arrayBuffer)
    uint8Array.set(data)
    Taro.request({
      url: cosUpload.uploadUrl,
      method: 'PUT',
      header: {
        'Content-Type': cosUpload.mimeType,
      },
      data: arrayBuffer,
      success(res) {
        resolve(res)
      },
      fail(err) {
        console.log('cos upload',err)
        reject(err)
      },
    })
  })
}
