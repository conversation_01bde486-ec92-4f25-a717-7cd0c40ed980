import { Button, Popup } from "@antmjs/vantui";
import { View } from "@tarojs/components";
import { pxTransform, getDeviceInfo } from "@tarojs/taro";
import './index.less'
import { useEffect, useState } from "react";
import classNames from "classnames";
type Props = {
  show: boolean,
  onClose: () => void,
  onConfirm: () => void,
}

const Index: React.FC<Props> = (props) => {
  const { show, onClose, onConfirm } = props
  const [isIOS, setIsIOS] = useState(false)

  useEffect(() => {
    const platform = getDeviceInfo().platform
    console.log('platform', platform)
    setIsIOS(platform === 'iOS')
  }, [])
  return  <Popup
    position='bottom'
    show={show}
    closeable={false}
    onClose={onClose}
    safeAreaInsetBottom
    round
    zIndex={110}
  >
    <View className='clear_popup_title'>确认清除上下文吗？</View>
    <View className='clear_popup_list'>清除后，AI的回复将不受上下文影响，不会清除历史聊天记录</View>
    <View className={classNames('clear_popup_actions', {
        'clear_popup_actions_safe': isIOS
    })}>
        <Button
            onClick={onClose}
            style={{
                '--padding-md': pxTransform(28),
                '--button-normal-height': pxTransform(96),
                color: '#777777'
            }}
            round
            block
            color='#F6F6F6'
        >
            取消
        </Button>
        <Button
            onClick={onConfirm}
            style={{
                '--padding-md': pxTransform(28),
                '--button-normal-height': pxTransform(96)
            }}
            round
            block
            color='linear-gradient(270deg, #6742FF 0.03%, #3D83FF 100.03%)'
        >
            确定
        </Button>
    </View>
  </Popup>
}

export default Index;
