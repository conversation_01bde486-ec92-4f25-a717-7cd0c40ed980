import StorageEnvKey from "@/constants/StorageEnvKey";
import { useEvent } from "@/utils/react-utils";
import StorageUtils from "@/utils/StorageUtils";
import { isFunction, isNil } from "lodash";
import { SetStateAction, useState } from "react";

/**
 * 修改 Storage 状态
 *
 * @param data 需要修改的值
 * @param validTime 有效时间，单位 ms
 */
type setStateAction<T> = (data: React.SetStateAction<T | null>, validTime?: number) => void;

/**
 * 本地缓存和state同步 hook
 * 值为 null 或者 undefined 的时候会自动删除key
 *
 * @param key 键值
 * @param emptyValue 为空时的默认值 默认 null
 */
export function useStorage<T = any>(key: keyof typeof StorageEnvKey | string) {
  const [state, setState] = useState<T | null>(() => {
      return StorageUtils.get(key);
  });

  const _setState = useEvent((data: SetStateAction<T | null>, validTime?: number) => {
      setState((prevState) => {
          const value = isFunction(data) ? data(prevState) : data;
          const resetValue = isNil(value) ? null : value;
          StorageUtils.set(key, resetValue, validTime);
          return resetValue;
      });
  });

  return [state, _setState] as [T | null, setStateAction<T>];
}
