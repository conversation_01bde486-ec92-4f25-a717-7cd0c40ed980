export enum ChatActionSheetType {
  /** 返回时确认结束对话 */
  FINISH = 1,
  /** 对话已关闭 */
  OFF = 2,
  /** 对话中断 */
  INTERRUPT = 3,
  // 问答完成
  COMPLETE = 4
}

export enum VoiceInteraction {
  Auto = 1,
  Manual = 2
}

export enum VoiceStatus {
  Start, // 开始
  BeforeListen, // 开启聆听前
  Listening, // 语音聆听
  WaitListen, // 等待聆听
  Waiting, // 等待AI回复
  Speaking, // AI说话
  Cancel, // 触发取消发送
  Canceled, // 确定取消发送
  Overtime, // 聆听超时
  Standby, // 聆听待机
  Stop, // 停止
  Complete, // 完成答题,
  Loading // 加载中
}
export enum RecognizeStatus {
  Start, // 识别开始
  SentenceBegin, // 一句话开始
  SentenceChange, // 识别变化中
  SentenceEnd, // 一句话完成
  RecorderStop, // 录音停止
  Error, // 识别错误
  Complete // 识别结束
}

export enum SpeechStatus {
  PlayStart,
  Playing,
  PlayEnd
}


export enum ChatMode {
  AUDIO = 1,
  SHUZIREN = 2
}

export enum ConnectStatus {
  CONNECTING = 1,
  CONNECTED = 2,
  START =3
}

export enum RoleEnum {
  USER = 'user',
  ASSISTANT = 'assistant'
}

export enum SubtitleVisible {
  Show = 1,
  Hide = 2
}

