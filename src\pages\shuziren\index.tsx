import { View, Image, Text, Block } from '@tarojs/components'
import {
  useRouter,
  showToast,
  useUnload,
  setNavigationBarTitle,
  createSelectorQuery,
  NodesRef,
  useReady,
  base64ToArrayBuffer,
  showLoading,
  hideLoading,
  pxTransform,
  getCurrentPages,
} from '@tarojs/taro'
import PictureHuman from '@bddh/starling-dh-picture-client'
import type { PictureHumanType } from '@bddh/starling-dh-picture-client'
import {
  ReactNode,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react'
import config from '@/config'
import {
  clearContext,
  doneChat,
  doneGenerateReport,
  generateReport,
  getAllChatHistory,
  getChat,
  getScript,
  qaAnswerRewrite,
  qaCompletions,
  qaSave,
  saveIntroduction,
} from '@/service/chat'
import { ChatVO, HistoryVO, QaItem } from '@/types/chat'
import { ScriptType } from '@/constants/scriptType'
import { EmanType } from '@/constants/emanType'
import LottieView, { LottieViewType } from 'taro-lottie'

import {
  ChatActionSheetType,
  VoiceInteraction,
  VoiceStatus,
  SpeechStatus,
  ChatMode,
  ConnectStatus,
  RoleEnum,
  SubtitleVisible,
  RecognizeStatus,
} from '@/constants/chatType'
import classNames from 'classnames'
import wx from 'weixin-js-sdk'

import { ActionSheet, Button, Dialog, Toast } from '@antmjs/vantui'
import { getServerTime, ttsVoice, uploadLog } from '@/service/common'
import dayjs from 'dayjs'
import { useCountDown, useThrottleFn } from 'ahooks'
import StorageEnvKey from '@/constants/StorageEnvKey'
import StorageUtils from '@/utils/StorageUtils'
import WebAudioSpeechRecognizer from '@/utils/tencentSpeechRecognizer/webaudiospeechrecognizer'
import VoiceLoading from '@/components/voiceLoading'
import { eManStatus } from '@/service/eman'

import { useChatStream } from '@/hooks/useChatStream'

import LottieWave from '@/assets/lottie_wave.json'
import LottieBubble from '@/assets/lottie_bubble.json'
import IconChat from '@/assets/icon_chat.svg'
import IconChatDisabled from '@/assets/icon_chat_disabled.svg'
import IconVideo from '@/assets/icon_video.svg'
import IconVideoClose from '@/assets/icon_video_close.svg'
import IconVideoDisabled from '@/assets/icon_video_disabled.svg'
import IconMore from '@/assets/icon_more.svg'
import IconHangup from '@/assets/icon_hangup.svg'
import IconHangupMini from '@/assets/icon_hangup_mini.svg'
import IconCancel from '@/assets/icon_cancel.svg'
import AvatarDefault from '@/assets/avatar_default.png'
import IconComplete from '@/assets/icon_complete.svg'
import errorImg from '@/assets/error.svg'
import IconSubtitleOff from '@/assets/icon_subtitle_off.svg'
import IconSubtitleOn from '@/assets/icon_subtitle_on.svg'
import IconSpeak from '@/assets/icon_speak.svg'
import IconSpeakDisabled from '@/assets/icon_speak_disabled.svg'
import { ruleMarkdownImage } from '@/utils/regexRules'
// import audioSpeechRecognizerFlash from '@/utils/tencentSpeechRecognizer/audioSpeechRecognizerFlash'
import { useEvent } from '@/utils/react-utils'
import ActionMore from '@/components/actionMore'
import ScriptActionSheet from '@/components/scriptActionSheet'
import DialogClearContext from '@/components/dialogClearContext'
import ActionVoice from '@/components/actionVoice'
import FeedbackDialog from '@/components/feedbackDialog'
// import base64ToBlob from '@/utils/base64ToBlob'
import { audioUploadComplete, cosUpload } from '@/service/cos'
import { calculatePcmDuration } from '@/utils/audioUtils'
import EventEmitter from '@/utils/EventEmitter'
import './index.less'
import { FeedbackData, LogInfo } from '@/types/common'
import FeedbackSucess from '@/components/feedbackSucess'
const Dialog_ = Dialog.createOnlyDialog()
const ctrl = new AbortController()

export default function Index() {
  const { params } = useRouter<{
    appId: string
    chatId: string
    from: string
    introductionType: string
    introductionDelay: string
    QCloudAIVoiceAppId: string
    QCloudAIVoiceSecretId: string
    QCloudAIVoiceSecretKey: string
  }>()
  // console.log(params, 'index')
  let {
    chatId,
    introductionType,
    introductionDelay,
    appId,
    QCloudAIVoiceAppId,
    QCloudAIVoiceSecretId,
    QCloudAIVoiceSecretKey,
  } = params

  const overtime = 5000 // 不说话超时时间
  const standbyTime = 5000 // 超时后待机时间
  const speechLength = 100 // 文本转换长度
  const defaultTone = {
    name: '',
    speed: 0.4, // [-2，2]，分别对应不同语速：-2代表0.6倍，-1代表0.8倍，0代表1.0倍（默认），1代表1.2倍，2代表1.5倍。如果需要更细化的语速，可以保留小数点后一位，例如0.5 1.1 1.8等
    volume: 0, // [0，10]
    voiceType: 301035, // 音色,爱小梅-多情感女声
  }
  const [chatData, setChatData] = useState<ChatVO>() // 对话数据
  const [emanType, setEmanType] = useState<EmanType>() // 普通数字人、智能数字人
  const [isShuziren, setIsShuziren] = useState(true) // 是否是数字人
  const [chatMode, setChatMode] = useState<ChatMode>() // 聊天模式，语音、数字人
  const [scriptType, setScriptType] = useState<ScriptType>() // 脚本类型，技巧、问答
  const [showScript, setShowScript] = useState(false)
  const [clearShow, setClearShow] = useState(false) // 清除上下文弹窗
  const [showMoreActionSheet, setShowMoreActionSheet] = useState(false) // 更多弹窗
  const [showVoiceActionSheet, setShowVoiceActionSheet] = useState(false) // 语音模式设置弹窗
  const [connecting, setConnecting] = useState(ConnectStatus.CONNECTING) // 数字人连接中
  const [connectingProgress, setConnectingProgress] = useState<number>(0) // 数字人连接中进度
  const progressInterval = useRef<any>()
  const [hasAudioAuth, setHasAudioAuth] = useState(true) // 语音权限
  const recognizeStatusRef = useRef<RecognizeStatus>()
  const [status, setStatus] = useState<VoiceStatus>() // 语音状态
  const statusRef = useRef<VoiceStatus>()
  const [statusText, setStatusText] = useState<string[]>()
  const [statusLoading, setStatusLoading] = useState<ReactNode>()

  const [voiceInteraction, setVoiceInteraction] = useState(
    VoiceInteraction.Auto,
  )
  const [sayPressed, setSayPressed] = useState<boolean>(false) // 是否按下说话按钮
  const intervalSpeech = useRef<any>() // 定时检查语音播放
  const intervalText = useRef<any>() // 定时检查ai文本
  const [isSpeaking, setIsSpeaking] = useState<boolean>(false) // 是否正在播放ai语音
  const isFirst = useRef<boolean>(false) // 是否是重新回复
  const tone = useRef({ ...defaultTone }) // ai声音
  const aiSentence = useRef<string[]>([]) // AI语音回复的文字
  const tempAiSentence = useRef<string>('') // 拼接ai文字
  const aiVoiceList = useRef<{ id: number; text: string; data?: string }[]>([])
  const aiVoiceNow = useRef<{
    id: number
    text: string
    data?: string
    status: SpeechStatus
  } | null>() // 当前ai语音播放
  const eventEmitterRef = useRef<any>()
  const questions = useRef<QaItem[]>([]) // 问题列表，保存回答用的
  const [questionList, setQuestionList] = useState<QaItem[]>([])
  const [questionNow, setQuestionNow] = useState(0) // 问答的第几个问题
  const historyRef = useRef<HistoryVO[]>([])
  const [footerTop, setFooterTop] = useState<number>(0)

  const audioInstance = useRef<HTMLAudioElement>()
  const speechRecognizerManager = useRef<WebAudioSpeechRecognizer>()
  const instancePictureHuman = useRef<PictureHumanType>()

  const bubbleAnimate = useRef<LottieViewType>()
  const waveAnimate = useRef<LottieViewType>()

  const timerOvertime = useRef<any>() // 5s不说话倒计时
  const timerStandby = useRef<any>() // 待机倒计时

  const [subtitleShow, setSubtitleShow] = useState<SubtitleVisible>(
    SubtitleVisible.Show,
  )
  const [subtitleText, setSubtitleText] = useState<string>('')

  const introducePcmData = useRef<string>()
  const introduceMp3Data = useRef<string>()

  const pictureHumanFinishTimer = useRef<any>() // 数字人播放完音频的时间
  const pictureHumanFinishTime = useRef<number>(0) // 数字人播放完音频的时间

  const userSentence = useRef<{
    i?: number
    text: string
    fileData?: []
    fileSize?: number
    chatHistoryId?: string
  }>() // 用户语音识别出来的文字

  const doneChatRef = useRef(false)

  // const [showAudioOpen, setShowAudioOpen] = useState<boolean>(false)
  const serverTimeTimer = useRef<any>()
  const [leftMinutes, setLeftMinutes] = useState<number>()
  const [leftSeconds, setLeftSeconds] = useState<number>()
  const timeRef = useRef({
    time: 0,
    createTime: '',
    serverTime: '',
    isTimeout: false,
    isOver: false,
    leftMinutes: undefined,
    leftSeconds: undefined,
  })
  const helloTimer = useRef<any>()
  const [timeLeftText, setTimeLeftText] = useState<string>()

  const [leftTime, setLeftTime] = useState<number>()

  const [chatActionSheetType, setChatActionSheetType] =
    useState<ChatActionSheetType>()
  const [showActionSheet, setShowActionSheet] = useState(false)

  const [recognizerTime, setRecognizerTime] = useState<number>(60)
  const recognizeTimeRef = useRef<any>()

  // const pictureHumanTimer = useRef<any>()

  const ttsVoiceSignal = useRef<AbortController[]>([])

  const [feedbackShow, setFeedbackShow] = useState<boolean>(false)
  const [feedbackSuccessShow, setFeedbackSuccessShow] = useState<boolean>(false)
  const feedbackData = useRef<FeedbackData>({
    client: 'h5',
    appId: '',
    path: '',
    chatId: '',
    description: '',
    logs: [],
  })

  let assistantMessageVal = ''

  const addLog = (log: LogInfo) => {
    const date = new Date()
    const params = {
      ...log,
      time: date.toLocaleString(),
      timestamp: date.getTime(),
    }
    console.log('log', params)
    wx.miniProgram.postMessage({
      data: {
        type: 'log',
        data: params,
      },
    })
    feedbackData.current.logs.push(params)
  }

  const changeStatus = (status: VoiceStatus) => {
    addLog({
      level: 'debug',
      message: 'changeStatus',
      data: {
        status,
      },
    })
    statusRef.current = status
    setStatus(status)
    switch (status) {
      case VoiceStatus.Listening:
        setStatusText(['我在听，你说'])
        setStatusLoading(<VoiceLoading type="barAnimate" />)
        break
      case VoiceStatus.WaitListen:
        setStatusText([''])
        setStatusLoading(<VoiceLoading type="barStatic" />)
        break
      case VoiceStatus.Waiting:
        setStatusText(['稍等，让我想想'])
        setStatusLoading(<VoiceLoading type="dot" />)
        break
      case VoiceStatus.Speaking:
        setStatusText(['点击可打断'])
        setStatusLoading(<VoiceLoading type="shortBar" />)
        break
      case VoiceStatus.Overtime:
        setStatusText(['我好像没听清', '请用普通话大声一点'])
        setStatusLoading(null)
        break
      case VoiceStatus.Standby:
        setStatusText(['点击屏幕重试'])
        setStatusLoading(<VoiceLoading type="repeat" />)
        break
      case VoiceStatus.Cancel:
        setStatusText(['松手取消发送'])
        setStatusLoading(<Image src={IconCancel} className="cancel_icon" />)
        break
      case VoiceStatus.Complete:
        setStatusText(['已完成全部答题'])
        setStatusLoading(<Image src={IconComplete} className="complete_icon" />)
        break
      default:
        setStatusText([''])
        setStatusLoading(null)
        break
    }
  }

  const clearRecognizerTimer = () => {
    addLog({
      level: 'trace',
      message: 'clearRecognizerTimer',
    })
    if (recognizeTimeRef.current) {
      clearInterval(recognizeTimeRef.current)
      recognizeTimeRef.current = undefined
    }
  }

  const caculateLeftTime = () => {
    return (
      dayjs(timeRef.current.createTime)
        .add(timeRef.current.time, 'm')
        .valueOf() - dayjs(timeRef.current.serverTime).valueOf()
    )
  }

  // 停止ai说话
  function stopAISpeaking() {
    addLog({
      level: 'trace',
      message: 'stopAISpeaking',
    })
    if (intervalSpeech.current) {
      clearInterval(intervalSpeech.current)
      intervalSpeech.current = null
    }
    if (intervalText.current) {
      clearInterval(intervalText.current)
      intervalText.current = null
    }
    tempAiSentence.current = ''

    assistantMessageVal = ''
    aiSentence.current = []
    aiVoiceList.current = []
    aiVoiceNow.current = null
    // audioInstance.current?.stop()
    // audioInstance.current?.offPlay()
    // audioInstance.current?.offTimeUpdate()
    // audioInstance.current?.offEnded()
    // audioInstance.current?.offError()
    audioInstance.current?.pause()
    setSubtitleText('')
    setIsSpeaking(false)
  }

  const finishVoice = useEvent(() => {
    addLog({
      level: 'trace',
      message: 'finishVoice',
      data: {
        questionNow,
      },
    })
    instancePictureHuman.current?.stop()
    if (emanType === EmanType.NORMAL) {
      if (scriptType === ScriptType.SKILL) {
        userSentence.current = undefined
        stopSpeechRecognizer()
        stopAISpeaking()
        changeStatus(VoiceStatus.Stop)
      } else if (scriptType === ScriptType.QUESTION) {
        if (
          questionNow === questionList.length - 1 &&
          questions.current[questionNow].answer
        ) {
          changeStatus(VoiceStatus.Complete)
          // stopSpeechRecognizer()
          stopAISpeaking()
        } else {
          // stopSpeechRecognizer()
          stopAISpeaking()
          changeStatus(VoiceStatus.Stop)
        }
      }
    } else if (emanType === EmanType.INTELLIGENCE) {
      stopSpeechRecognizer()
      stopAISpeaking()
      changeStatus(VoiceStatus.Stop)
      userSentence.current = undefined
    }
  })

  const doneActionSheet = useEvent(async () => {
    try {
      await doneChat(chatId, false)
      // 语音的情况
      finishVoice()
      doneChatRef.current = true
      setChatActionSheetType(ChatActionSheetType.OFF)
      setShowActionSheet(true)
    } catch (error) {
      addLog({
        level: 'error',
        message: 'countdown end request doneChat error',
        data: {
          error,
        },
      })
    }
  })

  const handleTimeOut = useEvent(() => {
    addLog({
      level: 'debug',
      message: 'handleTimeOut',
      data: {
        status: statusRef.current,
        voiceInteraction,
      },
    })
    if (emanType === EmanType.NORMAL) {
      if (voiceInteraction === VoiceInteraction.Auto) {
        if (
          statusRef.current === VoiceStatus.Listening ||
          statusRef.current === VoiceStatus.Overtime
        ) {
          // 语音识别中，停止识别
          stopSpeechRecognizer()
        } else {
          doneActionSheet()
        }
      } else if (voiceInteraction === VoiceInteraction.Manual) {
        if (statusRef.current === VoiceStatus.Listening) {
          changeStatus(VoiceStatus.Waiting)
          stopSpeechRecognizer()
          setSayPressed(false)
        } else if (statusRef.current === VoiceStatus.BeforeListen) {
          changeStatus(VoiceStatus.Canceled)
          setSayPressed(false)
          doneActionSheet()
        } else {
          doneActionSheet()
        }
      }
    }
  })

  const [, formattedRes] = useCountDown({
    // 此处默认且只能使用当前系统时间，如何拿服务器时间去做纠正 => 使用leftTime代替targetDate
    leftTime,
    // targetDate,
    onEnd: async () => {
      addLog({
        level: 'trace',
        message: 'countdown end',
      })
      if (!timeRef.current.isTimeout) {
        timeRef.current.isTimeout = true
        handleTimeOut()
        /* finishVoice()
        try {
          await doneChat(chatId, false)
          doneChatRef.current = true
          setChatActionSheetType(ChatActionSheetType.OFF)
          setShowActionSheet(true)
        } catch (error) {
          addLog({
            level: 'error',
            message: 'countdown end request doneChat error',
            data: {
              error,
            },
          })
        } */
      }
    },
  })

  const { minutes, seconds } = formattedRes

  useEffect(() => {
    if (
      timeRef.current.isOver &&
      timeRef.current.leftMinutes === undefined &&
      timeRef.current.leftSeconds === undefined
    ) {
      timeRef.current.leftMinutes = minutes
      timeRef.current.leftSeconds = seconds

      setLeftMinutes(minutes)
      setLeftSeconds(seconds)

      setLeftTime(undefined)
    }
    // console.log(minutes, seconds, isOver.current, leftMinutesRef.current, leftSecondsRef.current);
  }, [minutes, seconds])

  useEffect(() => {
    const m = leftMinutes !== undefined ? leftMinutes : minutes
    const s = leftSeconds !== undefined ? leftSeconds : seconds
    setTimeLeftText(`${m >= 10 ? m : `0${m}`}:${s >= 10 ? s : `0${s}`}`)
  }, [leftMinutes, leftSeconds, minutes, seconds])

  /*  const { cancel: cancelServerTime } = useRequest(() => getServerTime(), {
    pollingInterval: 30000, // 每隔30s更新一次当前时间，避免篡改手机本地时间导致倒计时错误
    onSuccess: async (data) => {
      timeRef.current.serverTime = data
      // console.log('getServerTime-createTime', createTime.current);
      if (timeRef.current.createTime) {
        const leftTime =
          dayjs(timeRef.current.createTime)
            .add(timeRef.current.time, 'm')
            .valueOf() - dayjs(timeRef.current.serverTime).valueOf()
        // const leftTime = Math.floor(Math.random() * (60000 - 10000 + 1) + 10000);
        console.log('getServerTime-leftTime', leftTime)
        setLeftTime(leftTime)
        if (leftTime <= 0) {
          cancelServerTime()
          if (!timeRef.current.isTimeout) {
            timeRef.current.isTimeout = true
            finishVoice()
            try {
              await doneChat(chatId, false)
              doneChatRef.current = true

              setChatActionSheetType(ChatActionSheetType.OFF)
              setShowActionSheet(true)
            } catch (error) {}
          }
        }
      }
    },
  }) */

  const syncTime = () => {
    serverTimeTimer.current = setInterval(() => {
      getServerTime().then(({ data }) => {
        timeRef.current.serverTime = data.data
        if (timeRef.current.createTime) {
          const leftTime =
            dayjs(timeRef.current.createTime)
              .add(timeRef.current.time, 'm')
              .valueOf() - dayjs(timeRef.current.serverTime).valueOf()
          // const leftTime = Math.floor(Math.random() * (60000 - 10000 + 1) + 10000);
          addLog({
            level: 'debug',
            message: 'getServerTime-leftTime',
            data: {
              createTime: timeRef.current.createTime,
              serverTime: timeRef.current.serverTime,
              leftTime,
            },
          })
          setLeftTime(leftTime)
          if (leftTime <= 0) {
            clearInterval(serverTimeTimer.current)
            if (!timeRef.current.isTimeout) {
              timeRef.current.isTimeout = true
              handleTimeOut()
              /* finishVoice()
              doneChat(chatId, false).then(() => {
                doneChatRef.current = true
                setChatActionSheetType(ChatActionSheetType.OFF)
                setShowActionSheet(true)
              }) */
            }
          }
        }
      })
    }, 30000)
  }
  const cancelServerTime = () => {
    if (serverTimeTimer.current) {
      clearInterval(serverTimeTimer.current)
    }
  }
  const splitSentence = (text: string) => {
    if (text.length <= speechLength) {
      aiSentence.current.push(text)
    } else {
      // 每speechLength长度的文本push到aiSentence.current
      const sentenceSplit = text.split(/[.?!;。？！]/g)
      for (let i = 0; i < sentenceSplit.length; i++) {
        const sp = sentenceSplit[i]
        if (sp.length <= speechLength) {
          if (sp !== '') {
            aiSentence.current.push(sp)
          }
        } else {
          for (let j = 0; j < sp.length; j += speechLength) {
            aiSentence.current.push(sp.substring(j, j + speechLength))
          }
        }
      }
    }
  }

  const handleSentence = (text: string) => {
    if (text !== undefined && text !== '') {
      if (isFirst.current && text.length >= 30) {
        // 如果是第一句，就简短，提升回复速度
        isFirst.current = false
        const firstPunctuationIndex = text.search(/[,.?!，。？！]/)
        if (
          firstPunctuationIndex !== -1 &&
          firstPunctuationIndex < text.length - 1
        ) {
          const firstPart = text.slice(0, firstPunctuationIndex)
          const secondPart = text.slice(firstPunctuationIndex + 1)

          // console.log('分割后的两个数组:', firstPart, secondPart);
          if (firstPart !== '') {
            if (firstPart.length <= 10) {
              aiSentence.current.push(firstPart)
            } else {
              splitSentence(firstPart)
            }
          }
          if (secondPart !== '') {
            splitSentence(secondPart)
          }
        } else {
          console.log('未找到指定的标点符号')
          splitSentence(text)
        }
      } else {
        splitSentence(text)
      }
      // splitSentence(text);
    }
  }

  const handleTtsError = (id: number) => {
    // 没有data删除
    const fi = aiVoiceList.current.findIndex((item) => item.id === id)

    if (fi !== -1) {
      if (aiVoiceNow.current && aiVoiceNow.current.id === id) {
        aiVoiceNow.current.status = SpeechStatus.PlayEnd
      }
      aiVoiceList.current.splice(fi, 1)
    }

    if (
      aiSentence.current.length === 0 &&
      aiVoiceList.current.length === 0 &&
      (!aiVoiceNow.current ||
        aiVoiceNow.current?.status === SpeechStatus.PlayEnd)
    ) {
      stopAISpeaking()
      if (!doneChatRef.current) {
        if (voiceInteraction === VoiceInteraction.Manual) {
          changeStatus(VoiceStatus.WaitListen)
        } else {
          changeStatus(VoiceStatus.Listening)
          startSpeechRecognizer()
        }
      }
    }
  }

  const textToSpeech = useEvent((text: string) => {
    const id = new Date().getTime()
    aiVoiceList.current.push({
      id,
      text,
      data: undefined,
    })
    addLog({
      level: 'debug',
      message: '开始ai文本转语音',
      data: {
        id,
        text,
        chatMode,
        encoding: chatMode === ChatMode.SHUZIREN ? 'pcm' : 'mp3',
        tone: { ...tone.current },
      },
    })

    const ctrlTts = new AbortController()
    ttsVoiceSignal.current.push(ctrlTts)

    ttsVoice(
      {
        name: tone.current.name,
        type: chatData.eman.gender,
        text:
          emanType === EmanType.NORMAL && scriptType === ScriptType.SKILL
            ? text.replace(/[\(（][^)]*[\)）]/g, '')
            : text,
        encoding: chatMode === ChatMode.SHUZIREN ? 'pcm' : 'mp3',
      },
      ctrlTts.signal,
    )
      .then((res) => {
        console.log('ttsvoice', res, statusRef.current)
        // if(status === VoiceStatus.WaitListen || status === VoiceStatus.Listening) {

        //   return;

        // } {
        if (res.data.data) {
          aiVoiceList.current.forEach((item) => {
            if (item.id === id) {
              item.data = res.data.data
            }
          })
          addLog({
            level: 'debug',
            message: 'tts success',
            data: {
              id,
              text,
            },
          })
        } else {
          addLog({
            level: 'warn',
            message: 'tts no data',
            data: {
              id,
              text,
            },
          })
          showToast({
            title: '语音生成错误',
            icon: 'none',
          })
          handleTtsError(id)
        }
        // }
      })
      .catch((error) => {
        if (error.statusCode && error.statusCode === 200) {
          if (error.data.message) {
              switch (error.data.message) {
                  case 'LimitExceeded.AccessLimit':
                  case '3031':
                  case '3003':
                      showToast({
                          title: '当前访问人数过多，请稍后重试',
                          icon: 'none'
                      });
                      break;
                  default:
                      showToast({
                          title: '语音生成错误',
                          icon: 'none'
                      });
                      break;
              }
          } else {
              showToast({
                  title: '语音生成错误',
                  icon: 'none'
              });
          }
      } else {
          showToast({
              title: '语音生成错误',
              icon: 'none'
          });
      }
        handleTtsError(id)
        addLog({
          level: 'error',
          message: 'tts error',
          data: {
            id,
            text,
            error,
          },
        })
      })
  })

  const handleErrorDialog = (msg: string = ' 网络异常，请刷新后重试') => {
    addLog({
      level: 'error',
      message: 'handleErrorDialog',
      data: {
        msg,
      },
    })
    Dialog_.confirm({
      message: (
        <Block>
          <Image
            style={{ width: pxTransform(240), height: pxTransform(240) }}
            src={errorImg}
          />
          <View
            style={{
              fontSize: pxTransform(32),
              textAlign: 'center',
              marginTop: pxTransform(56),
              marginBottom: pxTransform(56),
            }}
          >
            {msg}
          </View>
        </Block>
      ),
      confirmButtonText: '刷新',
      confirmButtonColor: '#4F66FF',
      onConfirm() {
        window.location.reload()
      },
    })
  }

  const textToSpeechIntroduce = useEvent((text, encoding) => {
    addLog({
      level: 'debug',
      message: 'textToSpeechIntroduce',
      data: {
        text,
        encoding,
      },
    })
    const ctrlTts = new AbortController()
    ttsVoiceSignal.current.push(ctrlTts)
    ttsVoice(
      {
        name: tone.current.name,
        type: chatData.eman.gender,
        text,
        encoding,
      },
      ctrlTts.signal,
    )
      .then((res) => {
        if (statusRef.current === VoiceStatus.WaitListen) {
          return
        } else {
          if (res.data.data) {
            if (encoding === 'pcm') {
              introducePcmData.current = res.data.data
            } else {
              introduceMp3Data.current = res.data.data
            }
          } else {
            addLog({
              level: 'warn',
              message: 'tts introduce no data',
              data: {
                text,
              },
            })
          }
        }
      })
      .catch((error) => {
        ttsVoiceSignal.current = []
        addLog({
          level: 'error',
          message: 'tts introduce error',
          data: {
            text,
            error,
          },
        })
      })
  })

  // 流式播报
  const handleTotalAudioCommand = useEvent(
    (audioData: string, text: string, first: boolean, last: boolean) => {
      if (!audioData) return

      changeStatus(VoiceStatus.Speaking)
      const pcmAudio = base64ToArrayBuffer(audioData)
      const duration = calculatePcmDuration(pcmAudio)
      pictureHumanFinishTime.current += duration
      addLog({
        level: 'debug',
        message: '开始驱动 pcmAudio',
        data: {
          first,
          last,
          duration,
          text,
          finishTime: pictureHumanFinishTime.current,
        },
      })
      const config: any = {
        audioBuffer: pcmAudio,
        last,
        first,
        sample: 16000,
        interrupted: false,
      }
      if (last) {
        clearTimeout(pictureHumanFinishTimer.current)
        pictureHumanFinishTimer.current = setTimeout(
          () => {
            addLog({
              level: 'debug',
              message: '主动结束播报',
            })
            clearTimeout(pictureHumanFinishTimer.current)
            pictureHumanFinishTime.current = 0
            const i = aiVoiceList.current.findIndex(
              (item) => item.id === aiVoiceNow.current?.id,
            )
            if (i === aiVoiceList.current.length - 1) {
              // 最后一个
              addLog({
                level: 'debug',
                message: '主动所有音频播报结束',
                data: {
                  finishTime: pictureHumanFinishTime.current,
                },
              })
              aiVoiceList.current = []
              aiVoiceNow.current = undefined
              ttsVoiceSignal.current = []
              stopAISpeaking()
              if (!doneChatRef.current) {
                if (voiceInteraction === VoiceInteraction.Manual) {
                  changeStatus(VoiceStatus.WaitListen)
                } else {
                  changeStatus(VoiceStatus.Listening)
                  startSpeechRecognizer()
                }
              }
            }
          },
          pictureHumanFinishTime.current * 1000 + 2500,
        )

        config.finishListener = (res) => {
          clearTimeout(pictureHumanFinishTimer.current)
          pictureHumanFinishTime.current = 0
          addLog({
            level: 'trace',
            message: 'finishListener',
            data: {
              finishRes: res,
            },
          })
          const i = aiVoiceList.current.findIndex(
            (item) => item.id === aiVoiceNow.current?.id,
          )
          if (i === aiVoiceList.current.length - 1) {
            // 最后一个

            addLog({
              level: 'trace',
              message: 'finishListener 所有音频播报结束',
            })
            aiVoiceList.current = []
            aiVoiceNow.current = undefined
            ttsVoiceSignal.current = []
            stopAISpeaking()
            if (!doneChatRef.current) {
              if (voiceInteraction === VoiceInteraction.Manual) {
                changeStatus(VoiceStatus.WaitListen)
              } else {
                changeStatus(VoiceStatus.Listening)
                startSpeechRecognizer()
              }
            }
          }
        }
      }
      setSubtitleText(subtitleText + text)
      eventEmitterRef.current.emit('audioData', config)
    },
  )

  /*  const handleTotalAudioCommand = (
    audio: ArrayBuffer,
    finishListener: (res: any) => void,
  ) => {
    addLog({
      level: 'debug',
      message: 'handleTotalAudioCommand',
      data: {
        last: true,
        first: true,
        sample: 16000,
        interrupted: true,
      },
    })
    instancePictureHuman.current?.audioRender({
      audioBuffer: audio,
      last: true,
      first: true,
      sample: 16000,
      interrupted: true,
      finishListener,
    })
  } */
  // 播放ai音频
  const handleVoicePlay = useEvent(async () => {
    if (chatMode === ChatMode.AUDIO) {
      if (aiVoiceList.current && aiVoiceList.current.length > 0) {
        // 语音队列中的第一条
        const [currentSpeech] = aiVoiceList.current
        // 如果当前已经播放完毕，则下一条
        if (
          !aiVoiceNow.current ||
          aiVoiceNow.current.status === SpeechStatus.PlayEnd
        ) {
          aiVoiceNow.current = {
            ...currentSpeech,
            status: SpeechStatus.PlayStart,
          }
        }
        const audioData = currentSpeech.data
        if (aiVoiceNow.current.status === SpeechStatus.PlayStart && audioData) {
          console.log(`开始播放语音`, aiVoiceNow.current, audioInstance.current)
          aiVoiceNow.current.status = SpeechStatus.Playing
          aiVoiceNow.current.data = audioData
          // 对话模式是语音
          changeStatus(VoiceStatus.Speaking)
          if (!audioInstance.current) return

          audioInstance.current.pause()
          audioInstance.current.currentTime = 0
          /* audioInstance.current.src = URL.createObjectURL(
            base64ToBlob(aiVoiceNow.current.data, 'mp3'),
          ) */
          audioInstance.current.src = `data:audio/mp3;base64,${aiVoiceNow.current.data}`
          setSubtitleText(aiVoiceNow.current?.text)
          // audioInstance.current.seek(0.1)
          // audioInstance.current.onPlay(() => {
          //   console.log(`语音onPlay${aiVoiceNow.current?.text}`, Date.now())
          // })

          /* if (voiceInteraction === VoiceInteraction.Auto) {
            audioInstance.current.onTimeUpdate(() => {
                  console.log('播放进度', audioInstance.current.currentTime, audioInstance.current.duration, audioInstance.current.duration - audioInstance.current.currentTime);

                  if (aiVoiceNow.current && aiVoiceNow.current.url) {
                      if (Math.round((audioInstance.current.duration - audioInstance.current.currentTime) * 10) / 10 <= 0.1) {
                          console.log('提前结束', audioInstance.current.duration, audioInstance.current.currentTime);
                          audioInstance.current.offPlay();
                          audioInstance.current.offTimeUpdate();
                          audioInstance.current.offEnded();
                          audioInstance.current.offError();
                          if (currentSpeech.id === aiVoiceNow.current.id) {
                              aiVoiceList.current.shift();
                              aiVoiceNow.current.status = SpeechStatus.PlayEnd;
                              console.log('剩余语音列表', aiVoiceList.current);
                          }
                      }
                  }
              });
          } */

          /* audioInstance.current.onEnded(() => {
            // 播放完，删除第一条
            console.log('播放完了', aiVoiceNow.current, Date.now())
            audioInstance.current?.offPlay()
            audioInstance.current?.offTimeUpdate()
            audioInstance.current?.offEnded()
            audioInstance.current?.offError()
            if (aiVoiceNow.current) {
              if (currentSpeech.id === aiVoiceNow.current.id) {
                aiVoiceList.current.shift()
                aiVoiceNow.current.status = SpeechStatus.PlayEnd
                console.log('剩余语音列表', aiVoiceList.current)
              }
            }
          }) */

          const endedEvent = () => {
            addLog({
              level: 'debug',
              message: '音频模式，播放结束',
              data: {
                id: aiVoiceNow.current?.id,
                text: aiVoiceNow.current?.text,
                status: aiVoiceNow.current?.status,
                aiVoiceLength: aiVoiceList.current.length,
              },
            })
            // setSubtitleText('')
            if (aiVoiceNow.current) {
              if (currentSpeech.id === aiVoiceNow.current.id) {
                aiVoiceList.current.shift()
                aiVoiceNow.current.status = SpeechStatus.PlayEnd
                console.log('剩余语音列表', aiVoiceList.current)
              }
            }
            audioInstance.current?.removeEventListener('ended', endedEvent)
          }

          audioInstance.current.addEventListener('ended', endedEvent)

          /*  audioInstance.current.onError((res) => {
            if (aiVoiceNow.current) {
              if (currentSpeech.id === aiVoiceNow.current.id) {
                aiVoiceList.current.shift()
                aiVoiceNow.current.status = SpeechStatus.PlayEnd
              }
            }

            console.error('audio error', res.errMsg)
          }) */

          addLog({
            level: 'debug',
            message: '音频播放模式,开始播放语音',
            data: {
              id: aiVoiceNow.current.id,
              text: aiVoiceNow.current.text,
              status: aiVoiceNow.current.status,
            },
          })
          const playPromise = audioInstance.current.play()

          if (playPromise !== undefined) {
            playPromise
              .then(() => {
                console.log('播放语音', audioInstance.current)
              })
              .catch((error) => {
                console.log('播放语音失败', { ...aiVoiceNow.current })
                setSubtitleText('')
                if (aiVoiceNow.current) {
                  if (currentSpeech.id === aiVoiceNow.current.id) {
                    aiVoiceList.current.shift()
                    aiVoiceNow.current.status = SpeechStatus.PlayEnd
                  }
                }
                handleErrorDialog('音频播放异常，请刷新后重试')

                addLog({
                  level: 'error',
                  message: 'audio error',
                  data: {
                    error,
                  },
                })
              })
          }
        }
      }
      if (
        aiSentence.current.length === 0 &&
        aiVoiceList.current.length === 0 &&
        (aiVoiceNow.current === null || aiVoiceNow.current.status === SpeechStatus.PlayEnd)
      ) {
        // ai回复完毕，继续聆听
        addLog({
          level: 'trace',
          message: 'ai回复完毕,继续聆听',
        })
        stopAISpeaking()
        if (!doneChatRef.current) {
          if (voiceInteraction === VoiceInteraction.Manual) {
            changeStatus(VoiceStatus.WaitListen)
          } else {
            changeStatus(VoiceStatus.Listening)
            startSpeechRecognizer()
          }
        }
      }
    } else if (chatMode === ChatMode.SHUZIREN) {
      if (aiVoiceList.current && aiVoiceList.current.length > 0) {
        // 数字人模式

        // 流式播报
        let first = true,
          last = true
        if (aiVoiceList.current.length === 1) {
          // 只有一个
          if (
            aiVoiceNow.current?.id === aiVoiceList.current[0].id ||
            !aiVoiceList.current[0].data
          ) {
            return
          }

          first = true
          last = true
          aiVoiceNow.current = {
            ...aiVoiceList.current[0],
            status: SpeechStatus.PlayStart,
          }
          handleTotalAudioCommand(
            aiVoiceNow.current.data,
            aiVoiceNow.current.text,
            first,
            last,
          )
        } else {
          // 多个的时候
          if (!aiVoiceNow.current) {
            // 第一个
            if (!aiVoiceList.current[0].data) return
            first = true
            last = false
            aiVoiceNow.current = {
              ...aiVoiceList.current[0],
              status: SpeechStatus.PlayStart,
            }
            handleTotalAudioCommand(
              aiVoiceNow.current.data,
              aiVoiceNow.current.text,
              first,
              last,
            )
          } else {
            if (
              aiVoiceNow.current.id ===
              aiVoiceList.current[aiVoiceList.current.length - 1].id
            ) {
              return
            }

            const i = aiVoiceList.current.findIndex(
              (item) => item.id === aiVoiceNow.current?.id,
            )
            // 下一个
            const next = i + 1
            if (!aiVoiceList.current[next]?.data) return
            first = false
            last = false
            if (next === aiVoiceList.current.length - 1) {
              // 最后一个
              last = true
            }
            aiVoiceNow.current = {
              ...aiVoiceList.current[i + 1],
              status: SpeechStatus.PlayStart,
            }

            handleTotalAudioCommand(
              aiVoiceNow.current.data,
              aiVoiceNow.current.text,
              first,
              last,
            )
          }
        }

        /* const [currentSpeech] = aiVoiceList.current
        // 如果当前已经播放完毕，则下一条
        if (
          !aiVoiceNow.current ||
          aiVoiceNow.current.status === SpeechStatus.PlayEnd
        ) {
          aiVoiceNow.current = {
            ...currentSpeech,
            status: SpeechStatus.PlayStart,
          }
        }
        const audioData = currentSpeech.data
        // console.log(`当前语音`, currentSpeech, aiVoiceNow.current)
        if (aiVoiceNow.current.status === SpeechStatus.PlayStart && audioData) {
          console.log(`开始播放语音`, aiVoiceNow.current)
          changeStatus(VoiceStatus.Speaking)
          aiVoiceNow.current.status = SpeechStatus.Playing
          aiVoiceNow.current.data = audioData

          clearTimeout(pictureHumanTimer.current)
          setSubtitleText(aiVoiceNow.current?.text)
          const pcmAudio = base64ToArrayBuffer(audioData)
          const duration = calculatePcmDuration(pcmAudio)

          pictureHumanTimer.current = setTimeout(
            () => {
              console.log(
                '主动播报完成',
                aiVoiceNow.current,
                aiVoiceList.current,
              )
              if (aiVoiceNow.current) {
                if (currentSpeech.id === aiVoiceNow.current.id) {
                  aiVoiceList.current.shift()
                  aiVoiceNow.current.status = SpeechStatus.PlayEnd
                  console.log('剩余语音列表', aiVoiceList.current)
                }
              }
              // setSubtitleText('')
              stopPictureHuman()
            },
            duration * 1000 + 2500,
          )
          addLog({
            level: 'debug',
            message: '开始驱动数字人播报',
            data: {
              duration,
              text: aiVoiceNow.current?.text,
            },
          })
          handleTotalAudioCommand(pcmAudio, (res) => {

            addLog({
              level: 'debug',
              message: '数字人播报完成',
              data: {
                res,
                id: aiVoiceNow.current?.id,
                text: aiVoiceNow.current?.text,
                status: aiVoiceNow.current?.status,
                aiVoiceLength: aiVoiceList.current.length,
              },
            })
            clearTimeout(pictureHumanTimer.current)
            // setSubtitleText('')
            if (aiVoiceNow.current) {
              aiVoiceList.current.shift()
              aiVoiceNow.current.status = SpeechStatus.PlayEnd
              console.log('剩余语音列表', [...aiVoiceList.current])
            }
          })
        } */
      }
      if (
        aiSentence.current.length === 0 &&
        aiVoiceList.current.length === 0 &&
        aiVoiceNow.current?.status === SpeechStatus.PlayEnd
      ) {
        // ai回复完毕，继续聆听
        console.log('ai回复完毕,继续聆听')
        stopAISpeaking()
        setIsSpeaking(false)
        if (!doneChatRef.current) {
          if (voiceInteraction === VoiceInteraction.Manual) {
            changeStatus(VoiceStatus.WaitListen)
          } else {
            changeStatus(VoiceStatus.Listening)
            startSpeechRecognizer()
          }
        }
      }
    }
  })

  const speakStart = useEvent(() => {
    if (doneChatRef.current) {
      return
    }
    addLog({
      level: 'trace',
      message: 'ai开始回复',
    })
    setIsSpeaking(true)
    isFirst.current = true
    if (intervalSpeech.current) {
      clearInterval(intervalSpeech.current)
    }

    if (intervalText.current) {
      clearInterval(intervalText.current)
    }
    intervalText.current = setInterval(() => {
      if (aiSentence.current && aiSentence.current.length > 0) {
        console.log('intervalText', aiSentence.current)
        const nextText = aiSentence.current.shift()
        console.log('nextText', nextText)
        console.log('剩余文本', aiSentence.current)
        if (nextText !== undefined && nextText.trim() !== '' && !/^[^\w\s\u4e00-\u9fa5]*$/.test(nextText)) {
          textToSpeech(nextText)
        }
      }
    }, 30)

    // 定时检查ai回复的语音列表
    intervalSpeech.current = setInterval(
      () => {
        handleVoicePlay()
      },
      chatMode === ChatMode.AUDIO ? 30 : 100,
    )
  })

  const speakEnd = useCallback(() => {
    addLog({
      level: 'debug',
      message: 'ai回复结束',
      data: {
        tempAiSentence: tempAiSentence.current,
      },
    })
    if (tempAiSentence.current) {
      if (emanType === EmanType.NORMAL) {
        handleSentence(tempAiSentence.current)
      } else if (emanType === EmanType.INTELLIGENCE) {
        // const fullSentence = tempAiSentence.current.replace(
        //   ruleMarkdownImage,
        //   '',
        // )
        const fullSentence = tempAiSentence.current.replace(
          ruleMarkdownImage,
          (_match, title, _url) => {
            return title || ''
          },
        )
        console.log('fullSentence', fullSentence)
        if (fullSentence) {
          handleSentence(fullSentence)
        }
      }
      tempAiSentence.current = ''
    } else {
    }

    isFirst.current = false
  }, [emanType])

  const speakProcess = (text: string) => {
    if (doneChatRef.current) {
      return
    }
    addLog({
      level: 'debug',
      message: 'ai回复中',
      data: {
        text,
      },
    })
    if (emanType === EmanType.INTELLIGENCE) {
      // 优化语音断句问题
      const t = text.replace(ruleMarkdownImage, '') // 先处理掉markdown图片
      const regex = /!\[/g
      if (!regex.test(t) && t.match(/[,.?!'";)，。？！’”；)》……——]$/)) {
        if (tempAiSentence.current) {
          let fullSentence = tempAiSentence.current + t
          tempAiSentence.current = ''
          console.log('fullSentence', fullSentence)

          fullSentence = fullSentence.replace(ruleMarkdownImage, '') // 去除markdown图片
          if (fullSentence) {
            handleSentence(fullSentence)
          }
        } else {
          let fullSentence = t
          fullSentence = fullSentence.replace(ruleMarkdownImage, '') // 去除markdown图片
          if (fullSentence) {
            handleSentence(fullSentence)
          }
        }
      } else {
        console.log('拼接')
        if (tempAiSentence.current) {
          tempAiSentence.current += text
        } else {
          tempAiSentence.current = text
        }
      }
    } else if (emanType === EmanType.NORMAL) {
      if (scriptType === ScriptType.QUESTION) {
        splitSentence(text)
      } else {
        // 优化语音断句问题
        if (text.match(/[,.?!'";)，。？！’”；)》……——]$/)) {
          if (tempAiSentence.current) {
            const fullSentence = tempAiSentence.current + text
            tempAiSentence.current = ''
            handleSentence(fullSentence)
          } else {
            handleSentence(text)
          }
        } else {
          console.log('拼接')
          if (tempAiSentence.current) {
            tempAiSentence.current += text
          } else {
            tempAiSentence.current = text
          }
        }
      }
    }
  }
  const answerComplete = async (i: number) => {
    if (timeRef.current.isTimeout) {
      doneActionSheet()
    } else {
      if (i === questions.current.length - 1) {
        addLog({
          level: 'trace',
          message: 'answerComplete 回答结束',
        })
        timeRef.current.isOver = true
        finishVoice()
        try {
          await doneChat(chatId, false)
          doneChatRef.current = true
        } catch (error) {
          addLog({
            level: 'error',
            message: 'answerComplete doneChat',
            data: {
              error,
            },
          })
        }
      }
    }
  }
  // 重写问题回答
  const handleSentenceRewrite = (path, i: number) => {
    addLog({
      level: 'debug',
      message: 'handleSentenceRewrite',
      data: {
        path,
        i,
        currentQuestion: questions.current[i],
      },
    })
    // if (i === null || i === undefined) return
    // if (questions.current[i].chatQAId && questions.current[i].chatHistoryId) {

    // }
    try {
      qaAnswerRewrite({
        chatQAId: questions.current[i].chatQAId,
        chatHistoryId: questions.current[i].chatHistoryId,
        key: path,
      })
    } catch (error) {
      addLog({
        level: 'error',
        message: 'qaAnswerRewrite',
        data: {
          error,
        },
      })
    }
    answerComplete(i)
  }

  const handleComplete = () => {
    addLog({
      level: 'trace',
      message: 'handleComplete',
    })
    setChatActionSheetType(ChatActionSheetType.COMPLETE)
    setShowActionSheet(true)
  }

  /* const rewriteRecognize = useEvent((i, fileData, fileSize, text) => {
    audioSpeechRecognizerFlash({
      appid: config.QCloudAIVoice.appId,
      secretid: config.QCloudAIVoice.secretId,
      secretkey: config.QCloudAIVoice.secretKey,
      data: fileData,
      dataLen: fileSize,
      voice_format: 'pcm',
      engine_type: '16k_zh_large', // 引擎类型
      hotword_id: config.QCloudAIVoice.hotword_id,
      customization_id: config.QCloudAIVoice.customization_id,
      success: (data: { request_id: string; result: string }) => {
        console.log('sentenceRecognition succ:', data.result)
        handleSentenceRewrite(data.result, i)
      },
      fail: async (err: any) => {
        console.log('sentenceRecognition fail:', err)
        handleSentenceRewrite(text, i)
      },
    })
  })
 */
  // 用于语音识别
  const sentenceRecognize = async ({ i, text, fileSize, fileData }) => {
    addLog({
      level: 'debug',
      message: 'sentenceRecognize',
      data: {
        i,
        text,
        fileSize,
        questions: questions.current[i],
      },
    })
    console.log('sentenceRecognize', questions.current[i].ossConfig, fileData)
    if (
      text &&
      questions.current[i].ossConfig &&
      questions.current[i].ossConfig.path
    ) {
      cosUpload(questions.current[i].ossConfig, fileData)
        .then(async (data) => {
          // 上传成功
          console.info('cosUpload Success', data)
          addLog({
            level: 'trace',
            message: 'cosUpload Success',
          })
          try {
            await audioUploadComplete(questions.current[i].ossConfig.path)
            historyRef.current.forEach((item) => {
              if (item.qaId && item.qaId === questions.current[i].qaId) {
                item['upload'] = true
              }
            })
            handleSentenceRewrite(questions.current[i].ossConfig.path, i)
            answerComplete(i)
          } catch (error) {
            // rewriteRecognize(i, fileData, fileSize, text)
            answerComplete(i)
            addLog({
              level: 'error',
              message: 'audioUploadComplete',
              data: {
                error,
              },
            })
          }
        })
        .catch((error) => {
          // 上传失败
          // rewriteRecognize(i, fileData, fileSize)
          answerComplete(i)
          addLog({
            level: 'error',
            message: 'cosUpload',
            data: {
              error,
            },
          })
        })
    } else {
      answerComplete(i)
      addLog({
        level: 'debug',
        message: 'cosUpload no text',
      })
    }
  }

  const { run: chatStreamRun } = useChatStream({
    ctrl,
    onProcess: (text) => {
      console.log(
        'onProcess-text',
        text,
        assistantMessageVal,
        new Date().getTime(),
        timeRef.current.isTimeout,
      )
      if (!timeRef.current.isTimeout) {
        if (!assistantMessageVal) {
          speakStart()
        }
        speakProcess(text) // 传给语音合成
        assistantMessageVal += text
      }
    },
    onEnd: () => {
      if (!timeRef.current.isTimeout) {
        assistantMessageVal = ''
        speakEnd() // 告诉语音界面ai输出结束
      }
    },
    onError: (error) => {
      if (error.errMsg) {
        if (error.errMsg.includes('timeout')) {
            showToast({
                title: '当前访问人数过多，响应有点慢',
                icon: 'none'
            });
        } else if (error.errMsg.includes('limit')) {
            showToast({
                title: '当前访问人数过多，请稍后重试',
                icon: 'none'
            });
        } else if (error.errMsg.includes('401')) {
            showToast({
                title: '登录已失效，请重新登录',
                icon: 'none'
            });
        }
    } else {
        showToast({
            title: '对话错误，请重试',
            icon: 'none'
        });
    }
      assistantMessageVal = ''
      // 继续聆听
      stopAISpeaking()
      stopPictureHuman()
      if (voiceInteraction === VoiceInteraction.Auto) {
        // 打断

        changeStatus(VoiceStatus.Standby)
      } else if (voiceInteraction === VoiceInteraction.Manual) {
        changeStatus(VoiceStatus.WaitListen)
      }
      addLog({
        level: 'error',
        message: 'chatStreamRun',
        data: {
          error,
        },
      })
    },
  })

  const nextQuestion = (qNow: number) => {
    const nextQuestion = questions.current[qNow]

    speakProcess(nextQuestion.question) // 传给语音合成
    historyRef.current.push({
      role: RoleEnum.ASSISTANT,
      content: nextQuestion.question,
    })
    addLog({
      level: 'trace',
      message: 'nextQuestion',
      data: {
        qNow,
        nextQuestion,
      },
    })
  }

  const saveQuestion = async (qNow: number) => {
    const nextQ = questions.current[qNow]
    addLog({
      level: 'debug',
      message: 'saveQuestion',
      data: {
        qNow,
        answer: questions.current[qNow].answer,
        nextQ,
      },
    })
    if (!questions.current[qNow].answer) {
      // 如果没有回答，则保存问题
      try {
        await qaSave(chatId, nextQ.qaId)
      } catch (error) {
        addLog({
          level: 'error',
          message: 'qaSave',
          data: {
            error,
          },
        })
      }
    }
  }

  const handleSendQuestion = useEvent(async (text: string, i: number) => {
    console.log('handleSendQuestion', text, i, questions.current)
    questions.current[i].answer = text
    historyRef.current.push({
      role: RoleEnum.USER,
      content: text,
      qaId: questions.current[i].qaId,
    })
    try {
      // 保存回答
      const audioFileName = `useraudio_${chatId}_${Date.now()}`
      const res = await qaCompletions(chatId, {
        qaId: questions.current[i].qaId,
        answer: text,
        audioFileName,
      })

      const uploadUrl = res.header['upload-url']
      const mimeType = res.header['mimetype'] // h5没有大小写
      const path = res.header['key']
      if (path) {
        questions.current[i] = {
          ...questions.current[i],
          ossConfig: {
            audioFileName,
            path,
            uploadUrl,
            mimeType,
          },
        }
      }

      addLog({
        level: 'debug',
        message: 'qaCompletions',
        data: {
          i,
          path,
          timeout: timeRef.current.isTimeout,
        },
      })
      const { chatQAId, chatHistoryId } = res.data.data
      questions.current[i].chatQAId = chatQAId
      questions.current[i].chatHistoryId = chatHistoryId

      if (userSentence.current) {
        userSentence.current.chatHistoryId = chatHistoryId
        if (userSentence.current?.fileData) {
          sentenceRecognize({ ...userSentence.current })
          addLog({
            level: 'trace',
            message: '有录音，发送重新识别',
          })
        }
      }
      userSentence.current = undefined
      if (timeRef.current.isTimeout) {
        // 如果超时就不进行下一题
        return
      }
      if (i < questions.current.length - 1) {
        const nextIndex = i + 1
        saveQuestion(nextIndex)
        setQuestionNow(nextIndex)

        nextQuestion(nextIndex)
        speakStart()
      } else {
        // timeRef.current.isOver = true
        cancelServerTime()
        changeStatus(VoiceStatus.Loading)
        // finishVoice()
        // setChatActionSheetType(ChatActionSheetType.COMPLETE)
        // setShowActionSheet(true)
        addLog({
          level: 'trace',
          message: '答题完了',
        })
      }
    } catch (error) {
      addLog({
        level: 'error',
        message: 'qaCompletions',
        data: {
          error,
        },
      })
    }
  })

  const handleSend = useEvent(async (text: string) => {
    addLog({
      level: 'debug',
      message: 'handleSend',
      data: {
        text,
      },
    })
    // 技巧类、智脑情况下，发送给AI
    chatStreamRun({
      chatId,
      message: text,
      audioFileName: Date.now().toString(),
    })
    if (timeRef.current.isTimeout) {
      doneActionSheet()
    }
  })

  const startSpeechRecognizer = () => {
    addLog({
      level: 'trace',
      message: 'startSpeechRecognizer 启动识别',
    })
    clearRecognizerTimer()
    setRecognizerTime(60)
    speechRecognizerManager.current?.start()
  }

  const stopSpeechRecognizer = () => {
    addLog({
      level: 'trace',
      message: 'stopSpeechRecognizer 停止识别',
    })
    clearRecognizerTimer()
    if (timerOvertime.current) {
      clearTimeout(timerOvertime.current)
      timerOvertime.current = null
    }
    if (timerStandby.current) {
      clearTimeout(timerStandby.current)
      timerStandby.current = null
    }

    speechRecognizerManager.current?.stop()
  }

  const handleRecognitionStart = useEvent((res) => {
    recognizeStatusRef.current = RecognizeStatus.Start
    addLog({
      level: 'debug',
      message: 'handleRecognitionStart',
      data: {
        res,
        voiceInteraction,
        emanType,
        status,
      },
    })
    if (voiceInteraction === VoiceInteraction.Auto) {
      if (timerOvertime.current) {
        clearTimeout(timerOvertime.current)
      }
      if (timerStandby.current) {
        clearTimeout(timerStandby.current)
      }
      if (statusRef.current === VoiceStatus.Canceled) return
      changeStatus(VoiceStatus.Listening)

      recognizeTimeRef.current = setInterval(() => {
        setRecognizerTime((prev) => {
          if (prev === 0) {
            clearRecognizerTimer()
            return 0
          }
          return prev - 1
        })
      }, 1000)

      timerOvertime.current = setTimeout(() => {
        // 超过5s没说话，进入超时阶段
        addLog({
          level: 'trace',
          message: '超时',
        })
        changeStatus(VoiceStatus.Overtime)
        if (timerStandby.current) {
          clearTimeout(timerStandby.current)
        }
        timerStandby.current = setTimeout(() => {
          addLog({
            level: 'trace',
            message: '待机',
          })
          stopSpeechRecognizer()
          changeStatus(VoiceStatus.Standby)
          if (timerOvertime.current) {
            clearTimeout(timerOvertime.current)
          }
          if (timerStandby.current) {
            clearTimeout(timerStandby.current)
          }
          userSentence.current = undefined
        }, standbyTime)
      }, overtime)
    } else if (voiceInteraction === VoiceInteraction.Manual) {
      if (statusRef.current === VoiceStatus.WaitListen) {
        stopSpeechRecognizer()
      } else {
        changeStatus(VoiceStatus.Listening)
      }
    }
  })

  const handleSentenceBegin = useEvent((res) => {
    recognizeStatusRef.current = RecognizeStatus.SentenceBegin
    addLog({
      level: 'debug',
      message: '一句话开始',
      data: {
        res,
        emanType,
        voiceInteraction,
        status: statusRef.current,
      },
    })

    if (voiceInteraction === VoiceInteraction.Auto) {
      if (timerOvertime.current) {
        clearTimeout(timerOvertime.current)
      }
      if (timerStandby.current) {
        clearTimeout(timerStandby.current)
      }
      if (statusRef.current === VoiceStatus.Canceled) return
      cancelHello()

      if (statusRef.current === VoiceStatus.Overtime) {
        changeStatus(VoiceStatus.Listening)
      }
    } else if (voiceInteraction === VoiceInteraction.Manual) {
      if (statusRef.current === VoiceStatus.WaitListen) {
        stopSpeechRecognizer()
      }
    }
  })

  const handleRecognitionResultChange = useEvent((res) => {
    recognizeStatusRef.current = RecognizeStatus.SentenceChange
    /* addLog({
      level: 'debug',
      message: '识别变化时',
      data: {
        res,
        emanType,
        voiceInteraction,
        status,
      },
    }) */
    // if (emanType === EmanType.NORMAL) {
    //   if (voiceInteraction === VoiceInteraction.Auto) {
    //     changeStatus(VoiceStatus.Listening)
    //   }
    // } else if (emanType === EmanType.INTELLIGENCE) {
    //   changeStatus(VoiceStatus.Listening)
    // }
  })

  const handleSentenceEnd = useEvent((res) => {
    recognizeStatusRef.current = RecognizeStatus.SentenceEnd
    addLog({
      level: 'debug',
      message: '一句话结束',
      data: {
        res,
        emanType,
        voiceInteraction,
        status: statusRef.current,
      },
    })
    if (timerOvertime.current) {
      clearTimeout(timerOvertime.current)
    }
    if (timerStandby.current) {
      clearTimeout(timerStandby.current)
    }

    const resSentence = res.result.voice_text_str
    if (
      (emanType === EmanType.NORMAL && scriptType === ScriptType.SKILL) ||
      emanType === EmanType.INTELLIGENCE
    ) {
      // 技巧类
      if (voiceInteraction === VoiceInteraction.Auto) {
        // 自动模式
        if (statusRef.current === VoiceStatus.Canceled) return
        if (
          statusRef.current === VoiceStatus.Listening ||
          statusRef.current === VoiceStatus.Overtime
        ) {
          if (resSentence.trim()) {
            handleSend(res.result.voice_text_str)
            stopSpeechRecognizer()
            changeStatus(VoiceStatus.Waiting)
          } else {
            stopSpeechRecognizer()
            changeStatus(VoiceStatus.Standby)
            if (timeRef.current.isTimeout) {
              doneActionSheet()
            }
            showToast({
              icon: 'none',
              title: '未识别到文字',
            })
            addLog({
              level: 'trace',
              message: '自动模式，未识别到文字,等待',
            })
          }
        }
      } else if (voiceInteraction === VoiceInteraction.Manual) {
        // 手动模式
        if (statusRef.current === VoiceStatus.Waiting) {
          if (resSentence.trim()) {
            handleSend(res.result.voice_text_str)
          } else {
            userSentence.current = undefined
            changeStatus(VoiceStatus.WaitListen)
            if (timeRef.current.isTimeout) {
              doneActionSheet()
            }
            showToast({
              icon: 'none',
              title: '未识别到文字',
            })
            addLog({
              level: 'trace',
              message: 'SentenceEnd 手动模式，未识别到文字,待机',
            })
          }
        } else if (statusRef.current === VoiceStatus.WaitListen) {
          stopSpeechRecognizer()
        }
      }
    } else if (scriptType === ScriptType.QUESTION) {
      // 问答类
      console.log('一句话结束问答类', { voiceInteraction, questionNow })
      if (voiceInteraction === VoiceInteraction.Auto) {
        // 自动模式
        if (
          userSentence.current === null ||
          userSentence.current === undefined
        ) {
          // 先流式识别出了
          addLog({
            level: 'trace',
            message: 'SentenceEnd 自动模式，文字先出',
          })
          if (resSentence.trim()) {
            userSentence.current = { i: questionNow, text: resSentence }
            if (
              statusRef.current === VoiceStatus.Listening ||
              statusRef.current === VoiceStatus.Overtime
            ) {
              if (questionNow < questionList.length - 1) {
                changeStatus(VoiceStatus.Waiting)
              }
              stopSpeechRecognizer()
            }
          } else {
            showToast({
              icon: 'none',
              title: '未识别到文字',
            })

            stopSpeechRecognizer()
            userSentence.current = undefined
            changeStatus(VoiceStatus.Standby)
            if (timeRef.current.isTimeout) {
              doneActionSheet()
            }
            addLog({
              level: 'trace',
              message: 'SentenceEnd 自动模式，未识别到文字,待机',
            })
          }
        } else {
          // 如果已经有录音

          userSentence.current.text = resSentence
          if (
            statusRef.current === VoiceStatus.Listening ||
            statusRef.current === VoiceStatus.Overtime
          ) {
            stopSpeechRecognizer()
            if (questionNow < questionList.length - 1) {
              changeStatus(VoiceStatus.Waiting)
            }
            if (resSentence.trim()) {
              handleSendQuestion(resSentence, questionNow)
            }
          }
          addLog({
            level: 'debug',
            message: 'SentenceEnd 自动模式，已有录音,进行保存',
            data: {
              text: userSentence.current.text,
              i: userSentence.current.i,
              fileSize: userSentence.current?.fileSize,
            },
          })
        }

        /* if (
            statusRef.current === VoiceStatus.Listening ||
            statusRef.current === VoiceStatus.Overtime
          ) {
            stopSpeechRecognizer()
            if (questionNow < questionList.length - 1) {
              changeStatus(VoiceStatus.Waiting)
            }
            if (resSentence.trim()) {
              handleSendQuestion(resSentence)
            }
          } */
      } else if (voiceInteraction === VoiceInteraction.Manual) {
        console.log(
          '问答手动模式',
          resSentence,
          userSentence.current,
          statusRef.current,
        )
        if (statusRef.current === VoiceStatus.Waiting) {
          if (resSentence.trim()) {
            if (
              userSentence.current === null ||
              userSentence.current === undefined
            ) {
              userSentence.current = { i: questionNow, text: resSentence }
              addLog({
                level: 'trace',
                message: 'SentenceEnd 手动模式，文字先出',
              })
            } else {
              // 如果已经有录音
              userSentence.current.text = resSentence
              handleSendQuestion(resSentence, questionNow)
              addLog({
                level: 'debug',
                message: 'SentenceEnd 手动模式，文字先出,进行保存',
                data: {
                  text: userSentence.current.text,
                  i: userSentence.current.i,
                  fileSize: userSentence.current?.fileSize,
                },
              })
            }
          } else {
            changeStatus(VoiceStatus.WaitListen)
            if (timeRef.current.isTimeout) {
              doneActionSheet()
            }
            showToast({
              icon: 'none',
              title: '未识别到文字',
            })
            addLog({
              level: 'trace',
              message: 'SentenceEnd 手动模式，未识别到文字,等待',
            })
          }
        } else if (statusRef.current === VoiceStatus.WaitListen) {
          stopSpeechRecognizer()
        }
      }
    }
  })

  const handleRecorderStop = useEvent((res) => {
    addLog({
      level: 'debug',
      message: '录音结束',
      data: {
        emanType,
        voiceInteraction,
        status: statusRef.current,
        fileSize: res.length,
      },
    })
    console.info('录音结束', res)

    if (emanType === EmanType.NORMAL) {
      if (scriptType === ScriptType.SKILL) {
        if (voiceInteraction === VoiceInteraction.Auto) {
          if (recognizeStatusRef.current === RecognizeStatus.Start) {
            if (timeRef.current.isTimeout) {
              doneActionSheet()
            }
          }
        }
      } else if (scriptType === ScriptType.QUESTION) {
        if (voiceInteraction === VoiceInteraction.Auto) {
          if (statusRef.current === VoiceStatus.Standby) {
            return
          }
          if (
            userSentence.current === undefined ||
            userSentence.current === null
          ) {
            // 如果文字还没识别，先创建，文字识别出来后改写
            addLog({
              level: 'trace',
              message: 'RecorderStop 自动模式，录音先出来',
            })
            if (recognizeStatusRef.current === RecognizeStatus.Start) {
              // 如果没有回调sentence
              if (timeRef.current.isTimeout) {
                doneActionSheet()
              }
            } else {
              userSentence.current = {
                i: questionNow,
                text: '',
                fileData: res,
                fileSize: res.length,
              }
            }
          } else {
            // 如果文字先识别，再去识别改写
            if (!userSentence.current.fileData) {
              userSentence.current.fileData = res
              userSentence.current.fileSize = res.length

              handleSendQuestion(userSentence.current.text, questionNow)
            }
            addLog({
              level: 'trace',
              message: 'RecorderStop 文字已有，得到录音，进行保存',
            })
          }

          if (timerOvertime.current) {
            clearTimeout(timerOvertime.current)
            timerOvertime.current = null
          }
          if (timerStandby.current) {
            clearTimeout(timerStandby.current)
            timerStandby.current = null
          }
        } else if (voiceInteraction === VoiceInteraction.Manual) {
          console.log('手动模式', questionNow, statusRef.current)
          if (statusRef.current === VoiceStatus.Waiting) {
            if (
              userSentence.current === undefined ||
              userSentence.current === null
            ) {
              addLog({
                level: 'trace',
                message: 'RecorderStop 录音先出来',
              })
              userSentence.current = {
                i: questionNow,
                text: '',
                fileData: res,
                fileSize: res.length,
              }
            } else {
              userSentence.current.fileData = res
              userSentence.current.fileSize = res.length

              handleSendQuestion(userSentence.current.text, questionNow)
              addLog({
                level: 'debug',
                message: 'RecorderStop 文字已有，得到录音，进行保存',
                data: {
                  text: userSentence.current.text,
                  i: userSentence.current.i,
                  fileSize: userSentence.current?.fileData?.length,
                  status: statusRef.current,
                },
              })
            }
          } else if (statusRef.current === VoiceStatus.WaitListen) {
            userSentence.current = undefined
          }
        }
      }
    }

    recognizeStatusRef.current = RecognizeStatus.RecorderStop
  })

  const handleRecognitionComplete = useEvent((res) => {
    recognizeStatusRef.current = RecognizeStatus.Complete
    addLog({
      level: 'debug',
      message: '识别结束',
      data: {
        res,
        status: statusRef.current,
      },
    })
  })

  const handleRecognitionError = useEvent((res) => {
    addLog({
      level: 'error',
      message: '识别错误',
      data: {
        ...res,
      },
    })
    recognizeStatusRef.current = RecognizeStatus.Error
    if (voiceInteraction === VoiceInteraction.Auto) {
      changeStatus(VoiceStatus.Standby)
      if (timerOvertime.current) {
        clearTimeout(timerOvertime.current)
      }
      if (timerStandby.current) {
        clearTimeout(timerStandby.current)
      }

      stopSpeechRecognizer()
    } else {
      userSentence.current = undefined
      changeStatus(VoiceStatus.WaitListen)
    }
    handleErrorDialog('语音识别异常，请刷新重试')
  })

  // 初始化语音识别
  const initSpeechRecognizer = useEvent(() => {
    console.log('初始化语音识别', { emanType, voiceInteraction })
    const voiceParams = {
      // 用户参数
      appid: config.QCloudAIVoice.appId,
      secretid: config.QCloudAIVoice.secretId,
      secretkey: config.QCloudAIVoice.secretKey,
      // 业务参数
      hotword_id: config.QCloudAIVoice.hotword_id,
      customization_id: config.QCloudAIVoice.customization_id,
      engine_model_type: '16k_zh_medical',
      needvad: 1, // 0：关闭 vad，1：开启 vad，默认为0。如果语音分片长度超过60秒，用户需开启 vad（人声检测切分功能）
      vad_silence_time: 1500, // 语音断句检测阈值，静音时长超过该阈值会被认为断句（多用在智能客服场景，需配合 needvad = 1 使用），取值范围：240-2000（默认1000），单位 ms
      noise_threshold: 0.2, // 噪音参数阈值，默认为0，取值范围：[-1,1]，对于一些音频片段，取值越大，判定为噪音情况越大。取值越小，判定为人声情况越大。慎用：可能影响识别效果
    }

    if (voiceInteraction === VoiceInteraction.Manual) {
      // 如果是按住说话的，取消说话检测
      voiceParams.needvad = 0
    }

    const webAudioSpeechRecognizer = new WebAudioSpeechRecognizer(
      voiceParams,
      true,
    )
    webAudioSpeechRecognizer.OnRecognitionStart = (res: any) => {
      handleRecognitionStart(res)
    }
    webAudioSpeechRecognizer.OnSentenceBegin = (res: any) => {
      handleSentenceBegin(res)
    }
    webAudioSpeechRecognizer.OnRecognitionResultChange = (res: any) => {
      handleRecognitionResultChange(res)
    }
    webAudioSpeechRecognizer.OnSentenceEnd = (res: any) => {
      handleSentenceEnd(res)
    }
    webAudioSpeechRecognizer.OnRecorderStop = (res: any) => {
      handleRecorderStop(res)
    }
    webAudioSpeechRecognizer.OnRecognitionComplete = (res: any) => {
      handleRecognitionComplete(res)
    }
    webAudioSpeechRecognizer.OnError = (res: any) => {
      handleRecognitionError(res)
    }
    speechRecognizerManager.current = webAudioSpeechRecognizer
  })

  // 初始化数字人
  const initPictureHuman = useEvent((modelUrl: string) => {
    return new Promise((resolve, reject) => {
      try {
        addLog({
          level: 'trace',
          message: '初始化数字人',
        })
        instancePictureHuman.current = new PictureHuman({
          // modelUrl: 'https://facefusion-2021-peoplesdaily.bj.bcebos.com/track/picturefigure/people_9sWYt7wmjuS7AKzRM3DLaa_v1_shoubai.zip?version=2',
          // modelUrl: config.shuziren.MODEL_URL,
          modelUrl,
          licenseUrl: config.shuziren.LICENSE_URL,
          licenseKey: config.shuziren.LICENSE_KEY,
          canvasId: '#canvas-pic',
          statusCallback: (res: any) => {
            console.info('PictureHuman', res)
            if (res.code === 0) {
              //
              // if (res.status.progress > connectingProgress) {
              //   setConnectingProgress(Math.floor(res.status.progress))
              // }
              if (res.status.progress == 100) {
                // 加载完成
                progressInterval.current &&
                  clearInterval(progressInterval.current)
                setConnectingProgress(res.status.progress)

                resolve(true)
              }
            } else {
              // 失败
              if (res.code === 2001) {
                resolve(true)
              } else {
                addLog({
                  level: 'error',
                  message: 'PictureHuman init error',
                  data: {
                    res,
                  },
                })
                reject(false)
              }
            }
          },
        })
      } catch (error) {
        reject(false)
      }
    })
  })

  const stopPictureHuman = () => {
    addLog({
      level: 'trace',
      message: '停止数字人',
    })
    if (pictureHumanFinishTimer.current) {
      clearTimeout(pictureHumanFinishTimer.current)
      pictureHumanFinishTime.current = 0
    }
    // pictureHumanTimer.current && clearTimeout(pictureHumanTimer.current)
    instancePictureHuman.current?.stop()
  }

  const destoryLottieAnimate = () => {
    bubbleAnimate.current?.pause()
    waveAnimate.current?.pause()
  }

  // 显示对话结束弹窗
  const showFinishActionSheet = () => {
    setChatActionSheetType(ChatActionSheetType.FINISH)
    setShowActionSheet(true)
  }

  const handleTextChat = useCallback(() => {
    addLog({
      level: 'trace',
      message: 'handleTextChat',
      data: {
        status: statusRef.current,
      },
    })
    if (
      statusRef.current === VoiceStatus.BeforeListen ||
      statusRef.current === VoiceStatus.Listening ||
      statusRef.current === VoiceStatus.Overtime
    ) {
      showToast({
        title: '不支持录音时切换',
        icon: 'none',
      })
    } else {
      if (emanType === EmanType.NORMAL) {
        wx.miniProgram.redirectTo({
          url: `/pages/chat/dialogue/index?chatId=${chatId}&from=shuziren`,
        })
      } else if (emanType === EmanType.INTELLIGENCE) {
        wx.miniProgram.redirectTo({
          url: `/pages/chat/enterpriseIntelligence/index?chatId=${chatId}`,
        })
      }
    }
  }, [emanType])

  // 直接结束，不生成报告
  const chatEnd = async () => {
    showLoading({
      title: '加载中',
      mask: true,
    })
    addLog({
      level: 'trace',
      message: 'chatEnd',
    })
    console.log(historyRef.current)
    const checkRewriteTimer = setInterval(async () => {
      const userAnswers = historyRef.current.filter(
        (item) =>
          item.role === RoleEnum.USER &&
          'upload' in item &&
          item.upload === false,
      )

      console.log('userAnswers', userAnswers)
      if (userAnswers.length === 0) {
        clearInterval(checkRewriteTimer)

        try {
          await doneChat(chatId, false)
          hideLoading()
          doneChatRef.current = true
          setShowActionSheet(false)

          wx.miniProgram.switchTab({ url: '/pages/home/<USER>' })
        } catch (error) {
          hideLoading()
          doneChatRef.current = true
          setShowActionSheet(false)

          wx.miniProgram.switchTab({ url: '/pages/home/<USER>' })
          addLog({
            level: 'error',
            message: 'chatEnd doneChat',
            data: {
              error,
            },
          })
        }
      }
    }, 100)
  }

  const openReport = async () => {
    showLoading({
      title: '加载中',
      mask: true,
    })
    addLog({
      level: 'trace',
      message: 'openReport',
    })
    console.log(historyRef.current)
    const checkRewriteTimer = setInterval(async () => {
      const userAnswers = historyRef.current.filter(
        (item) =>
          item.role === RoleEnum.USER &&
          'upload' in item &&
          item.upload === false,
      )
      console.log('userAnswers', userAnswers)
      if (userAnswers.length === 0) {
        clearInterval(checkRewriteTimer)
        console.info('结束，并生成报告')
        try {
          hideLoading()
          if (doneChatRef.current) {
            const res = await generateReport(chatId, true)
            if (res.data.code === 200) {
              wx.miniProgram.redirectTo({
                url: `/pages/chat/report/index?id=${chatData!.chatReportId}&to=home`,
              })
            }
          } else {
            const res = await doneGenerateReport(chatId, true)
            if (res.data.code === 200) {
              wx.miniProgram.redirectTo({
                url: `/pages/chat/report/index?id=${chatData!.chatReportId}&to=home`,
              })
            }
          }
        } catch (error) {
          addLog({
            level: 'error',
            message: 'openReport doneChat',
            data: {
              error,
            },
          })
          hideLoading()

          if (doneChatRef.current) {
            const res = await generateReport(chatId, true)
            if (res.data.code === 200) {
              wx.miniProgram.redirectTo({
                url: `/pages/chat/report/index?id=${chatData!.chatReportId}&to=home`,
              })
            }
          } else {
            const res = await doneGenerateReport(chatId, true)
            if (res.data.code === 200) {
              wx.miniProgram.redirectTo({
                url: `/pages/chat/report/index?id=${chatData!.chatReportId}&to=home`,
              })
            }
          }
        }
      }
    }, 100)
  }

  const changeChatMode = (mode: ChatMode) => {
    /*  if (mode === ChatMode.AUDIO) {
      if (!audioInstance.current) {
        // audioInstance.current = createInnerAudioContext()
        audioInstance.current = new Audio()
      }
    } else if (mode === ChatMode.SHUZIREN) {
      if (audioInstance.current) {
        audioInstance.current.pause()
      }
    } */
    addLog({
      level: 'trace',
      message: 'changeChatMode',
      data: {
        mode,
      },
    })
    setChatMode(mode)
    StorageUtils.set(StorageEnvKey.CHAT_MODE, mode)
  }

  const handleVideoChat = useEvent(async () => {
    addLog({
      level: 'trace',
      message: 'handleVideoChat',
      data: {
        isShuziren,
        chatMode,
      },
    })
    if (isShuziren) {
      // 有数字人，切换对话模式，并保存
      if (isSpeaking) {
        showToast({
          title: '说话过程中不支持切换视频',
          icon: 'none',
        })
      } else {
        const mode =
          chatMode === ChatMode.AUDIO ? ChatMode.SHUZIREN : ChatMode.AUDIO

        changeChatMode(mode)
        if (mode === ChatMode.SHUZIREN) {
          if (!instancePictureHuman.current) {
            // 如果没有初始化过，则重新初始化数字人
            setConnecting(ConnectStatus.CONNECTING)
            try {
              await initPictureHuman(chatData?.eman.zipFileUrl)
              console.log('数字人初始化成功')
              setConnecting(ConnectStatus.CONNECTED)
            } catch (error) {
              setConnectingProgress(100)

              changeChatMode(ChatMode.AUDIO)
              showToast({
                title: '数字人初始化失败',
                icon: 'error',
              })
            }
          }
        }
      }
    } else {
      // 没有数字人
      showToast({
        title: '该形象不支持3D形象',
        icon: 'none',
      })
    }
  })

  // 更多按钮
  const showMore = () => {
    setShowMoreActionSheet(true)
  }
  const showActionSheetVoiceSetting = () => {
    setShowMoreActionSheet(false)
    setShowVoiceActionSheet(true)
  }
  const voicePopupConfirm = () => {
    Dialog_.alert({
      title: '修改成功',
      message: '下次练习时生效',
      confirmButtonText: '关闭',
      confirmButtonColor: '#4F66FF',
    }).then(() => {})
  }
  const clearContextSend = async () => {
    setClearShow(false)

    const res = await clearContext(chatId)
    if (res.data.data) {
      // setOldHistory(() => {
      //     console.log(history, 'history');
      //     oldHistoryRef.current.push(...history);
      //     const all = JSON.parse(JSON.stringify(oldHistoryRef.current));
      //     return all.reverse();
      // });

      showToast({
        title: '已清除上下文',
        icon: 'none',
        duration: 1000,
      })
    }
  }

  const handleFeedback = () => {
    setShowMoreActionSheet(false)
    setFeedbackShow(true)
  }
  const submitFeedback = async (values: { description: string }) => {
    feedbackData.current.description = values.description
    try {
      const res = await uploadLog(JSON.stringify(feedbackData.current))
      if (res.data.data) {
        feedbackData.current.logs = []
        feedbackData.current.description = ''

        setFeedbackSuccessShow(true)
      }
    } catch (error) {
      showToast({
        title: '提交失败',
        icon: 'none',
      })
    }
  }

  // 按结束按钮
  const handleFinish = useEvent(() => {
    addLog({
      level: 'trace',
      message: 'handleFinish',
    })
    stopSpeechRecognizer()
    stopPictureHuman()
    if (emanType === EmanType.NORMAL) {
      showFinishActionSheet()
    } else if (emanType === EmanType.INTELLIGENCE) {
      // 智脑直接返回
      wx.miniProgram.navigateBack()
    }
  })

  const statusTextClick = useEvent(() => {
    addLog({
      level: 'trace',
      message: 'statusTextClick',
      data: {
        voiceInteraction,
        status: statusRef.current,
      },
    })
    if (voiceInteraction === VoiceInteraction.Auto) {
      // 打断
      if (statusRef.current === VoiceStatus.Speaking) {
        stopAISpeaking()
        stopPictureHuman()
        startSpeechRecognizer()
      }
      // 待机重试
      if (statusRef.current === VoiceStatus.Standby) {
        startSpeechRecognizer()
      }
    } else if (voiceInteraction === VoiceInteraction.Manual) {
      if (statusRef.current === VoiceStatus.Speaking) {
        stopAISpeaking()
        stopPictureHuman()
        changeStatus(VoiceStatus.WaitListen)
      }
      // 待机重试
      if (statusRef.current === VoiceStatus.Standby) {
        startSpeechRecognizer()
      }
    }
  })

  const toggleSubtitle = () => {
    setSubtitleShow((prev) => {
      const s =
        prev === SubtitleVisible.Hide
          ? SubtitleVisible.Show
          : SubtitleVisible.Hide
      if(s === SubtitleVisible.Show) {
        Taro.showToast({
          title: '已开启字幕',
          icon: 'none'
        })
      }
      StorageUtils.set(StorageEnvKey.SUBTITLE_VISIBLE, s)
      return s
    })
  }

  // 触摸按钮start
  const { run: handleTouchStart } = useThrottleFn(
    useEvent(() => {
      addLog({
        level: 'debug',
        message: 'handleTouchStart',
        data: {
          status: statusRef.current,
        },
      })
      if (statusRef.current === VoiceStatus.WaitListen) {
        if (
          emanType === EmanType.INTELLIGENCE ||
          (emanType === EmanType.NORMAL && scriptType === ScriptType.SKILL)
        ) {
          cancelHello()
        }
        setSayPressed(true)
        changeStatus(VoiceStatus.BeforeListen)
        startSpeechRecognizer()
      }
      if (statusRef.current === VoiceStatus.Speaking) {
        // 可打断
        stopPictureHuman()
        stopAISpeaking()

        setSayPressed(true)
        changeStatus(VoiceStatus.BeforeListen)
        startSpeechRecognizer()
      }

      // 1. ai还未返回，中断sse
      // 2. ai回复中，音频未开始转换：中断sse
      // 3. ai回复中且音频转换中: 中断sse且取消tts
      // 4. ai回复完成，音频转换中，播报中，取消tts
      // 4. ai回复完成，音频转换完成，播报中，取消tts

      /*  if( statusRef.current === VoiceStatus.Waiting) {

        changeStatus(VoiceStatus.WaitListen)
        ttsVoiceSignal.current.forEach((item) => {
          console.log('abort tts', item)
          try {
            item.abort({})
          } catch (error) {

          }

        })
        try {
          ctrl.abort()
        } catch (error) {
          console.error(error);
        }
        if (intervalSpeech.current) {
          clearInterval(intervalSpeech.current)
        }
        if (intervalText.current) {
          clearInterval(intervalText.current)
        }
        stopPictureHuman()
        stopAISpeaking()
        setSayPressed(true)
        startSpeechRecognizer()
      } */
    }),
    {
      wait: 1500,
      leading: true,
      trailing: false,
    },
  )

  // 触摸按钮end
  const { run: handleTouchEnd } = useThrottleFn(
    useEvent((e: any) => {
      console.log(
        'handleTouchEnd',
        statusRef.current,
        e.touches,
        e.changedTouches,
      )
      addLog({
        level: 'debug',
        message: 'handleTouchEnd',
        data: {
          status: statusRef.current,
        },
      })
      if (statusRef.current === VoiceStatus.Listening) {
        changeStatus(VoiceStatus.Waiting)
        stopSpeechRecognizer()

        setSayPressed(false)
      } else if (
        statusRef.current === VoiceStatus.Cancel ||
        statusRef.current === VoiceStatus.BeforeListen
      ) {
        changeStatus(VoiceStatus.WaitListen)
        setSayPressed(false)
        stopSpeechRecognizer()
      }
    }),
    {
      wait: 500,
      leading: false,
      trailing: true,
    },
  )

  const handleTouchMove = useEvent((e: any) => {
    if (
      statusRef.current === VoiceStatus.Listening ||
      statusRef.current === VoiceStatus.Cancel
    ) {
      if (footerTop) {
        console.log(
          'handleTouchMove',
          e.changedTouches[0].clientY,
          e.touches,
          footerTop,
        )
        if (e.changedTouches[0].clientY < footerTop) {
          // 超出区域
          console.log('handleTouchMove', '超出区域')
          changeStatus(VoiceStatus.Cancel)
        } else {
          changeStatus(VoiceStatus.Listening)
        }
      } else {
        createSelectorQuery()
          .select('#actionfooter')
          .boundingClientRect(
            (res: NodesRef.BoundingClientRectCallbackResult) => {
              if (
                statusRef.current === VoiceStatus.Listening ||
                statusRef.current === VoiceStatus.Cancel
              ) {
                console.log('handleTouchMove', e.changedTouches, e.touches, res)
                setFooterTop(res.top)
                if (e.changedTouches[0].clientY < res.top) {
                  // 超出区域
                  changeStatus(VoiceStatus.Cancel)
                } else {
                  changeStatus(VoiceStatus.Listening)
                }
              }
            },
          )
          .exec()
      }
    }
  })

  const actionSheet = useMemo(() => {
    if (chatActionSheetType === ChatActionSheetType.FINISH) {
      return {
        title: '确认结束练习吗？',
        actions: [
          {
            text: '结束并生成报告',
            buttonProps: {
              color: 'linear-gradient(270deg, #6742FF 0%, #3D83FF 100%)',
              onClick: openReport,
              // disabled: true
            },
            noChatHistoryHide: true,
          },
          {
            text: '直接结束',
            buttonProps: {
              color: '#F6F6F6',
              style: { color: '#777777' },
              onClick: chatEnd,
            },
          },
        ],
        closeAction: false, // 是否隐藏关闭按钮
      }
    }
    if (chatActionSheetType === ChatActionSheetType.COMPLETE) {
      return {
        title: '完成答题',
        actions: [
          {
            text: '生成报告',
            buttonProps: {
              color: 'linear-gradient(270deg, #6742FF 0%, #3D83FF 100%)',
              onClick: openReport,
              // disabled: true
            },
            noChatHistoryHide: true,
          },
          {
            text: '直接结束',
            buttonProps: {
              color: '#F6F6F6',
              style: { color: '#777777' },
              onClick: chatEnd,
            },
          },
        ],
        closeAction: true, // 是否隐藏关闭按钮
      }
    }
    if (chatActionSheetType === ChatActionSheetType.INTERRUPT) {
      return {
        title: '练习中断，是否继续？',
        actions: [
          {
            text: '继续对话',
            buttonProps: {
              color: 'linear-gradient(270deg, #6742FF 0%, #3D83FF 100%)',
              onClick: () => {
                setShowActionSheet(false)
                // TODO: 打断后，语音重新开始

                // startListen();
              },
            },
          },
          {
            text: '结束并生成报告',
            buttonProps: {
              color: '#F6F6F6',
              style: { color: '#777777' },
              onClick: openReport,
              // disabled: true
            },
            noChatHistoryHide: true,
          },
          {
            text: '直接结束',
            buttonProps: {
              color: '#F6F6F6',
              style: { color: '#777777' },
              onClick: chatEnd,
            },
          },
        ],
        closeAction: true,
      }
    }
    if (chatActionSheetType === ChatActionSheetType.OFF) {
      return {
        title: '练习时间结束，对话已关闭',
        actions: [
          {
            text: '生成报告',
            buttonProps: {
              color: 'linear-gradient(270deg, #6742FF 0%, #3D83FF 100%)',
              onClick: openReport,
              // disabled: true
            },
            noChatHistoryHide: true,
          },
          {
            text: '直接结束',
            buttonProps: {
              color: '#F6F6F6',
              style: { color: '#777777' },
              onClick: chatEnd,
            },
          },
        ],
        closeAction: true,
      }
    }
  }, [chatActionSheetType])

  useEffect(() => {
    if (!connecting) {
      createSelectorQuery()
        .select('#actionfooter')
        .boundingClientRect(
          (res: NodesRef.BoundingClientRectCallbackResult) => {
            console.log('boundingClientRect', res)
            setFooterTop(res.top)
          },
        )
        .exec()
    }
  }, [connecting])

  useEffect(() => {
    if (emanType === EmanType.NORMAL && scriptType === ScriptType.QUESTION) {
      setNavigationBarTitle({
        title: `第${questionNow + 1}/${questionList.length}题`,
      })
    }
  }, [questionNow])

  const checkAudio = () => {
    return new Promise((resolve, reject) => {
      navigator.mediaDevices
        .getUserMedia({ video: false, audio: true })
        .then((stream) => {
          try {
            window.localStream = stream // A
            window.localAudio.srcObject = stream // B
            window.localAudio.autoplay = true // C
          } catch (error) {}

          console.log(`you got a stream: ${stream}`)
          addLog({
            level: 'debug',
            message: 'checkAudio you got a stream:',
          })
          resolve(stream)
        })
        .catch((err) => {
          addLog({
            level: 'error',
            message: 'checkAudio you got a error',
            data: {
              error: err,
            },
          })
          reject(err)
        })
    })
  }

  const checkAudioAuth = async () => {
    audioInstance.current = new Audio()
    try {
      await checkAudio()
      setHasAudioAuth(true)
      setConnecting(ConnectStatus.START)
      initData()
    } catch (error) {
      setHasAudioAuth(false)
    }
  }
  // 取消开场白
  const cancelHello = () => {
    if (helloTimer.current) {
      clearTimeout(helloTimer.current)
      helloTimer.current = undefined
    }
  }

  const saveIntro = useEvent(async () => {
    addLog({
      level: 'debug',
      message: 'saveIntro',
      data: {
        introduce: chatData?.eman.introduction,
      },
    })
    try {
      changeStatus(VoiceStatus.Waiting)
      const saveIntroRes = await saveIntroduction(chatId)
      if (saveIntroRes.data) {
        speakStart()
        if (chatMode === ChatMode.AUDIO) {
          if (introduceMp3Data.current) {
            const id = new Date().getTime()
            aiVoiceList.current.push({
              id,
              text: chatData?.eman.introduction,
              data: introduceMp3Data.current,
            })
          } else {
            speakProcess(chatData?.eman.introduction) // 传给语音合成
          }
        } else if (chatMode === ChatMode.SHUZIREN) {
          if (introducePcmData.current) {
            const id = new Date().getTime()
            aiVoiceList.current.push({
              id,
              text: chatData?.eman.introduction,
              data: introducePcmData.current,
            })
          } else {
            speakProcess(chatData?.eman.introduction) // 传给语音合成
          }
        }

        speakEnd()
      } else {
        if (voiceInteraction === VoiceInteraction.Auto) {
          startSpeechRecognizer()
        } else if (voiceInteraction === VoiceInteraction.Manual) {
          changeStatus(VoiceStatus.WaitListen)
        }
      }
    } catch (error) {
      if (voiceInteraction === VoiceInteraction.Auto) {
        startSpeechRecognizer()
      } else if (voiceInteraction === VoiceInteraction.Manual) {
        changeStatus(VoiceStatus.WaitListen)
      }
    }
  })

  const initIntro = useEvent(() => {
    if (introductionType === '0') {
      addLog({
        level: 'debug',
        message: 'initIntro 没有开场白',
        data: {
          introduction: chatData?.eman.introduction,
          introductionType,
          introductionDelay,
        },
      })
      // 立即开场白，保存开场白，并语音播放

      if (voiceInteraction === VoiceInteraction.Auto) {
        startSpeechRecognizer()
      } else if (voiceInteraction === VoiceInteraction.Manual) {
        changeStatus(VoiceStatus.WaitListen)
      }
    } else if (introductionType === '1') {
      // 立即开场白，保存开场白，并语音播放
      addLog({
        level: 'debug',
        message: 'initIntro 立即开场白',
        data: {
          introduction: chatData?.eman.introduction,
          introductionType,
          introductionDelay,
        },
      })
      saveIntro()
    } else if (introductionType === '2') {
      // 延迟开场白，增加倒计时，倒计时结束，保存开场白，语音播放；

      if (voiceInteraction === VoiceInteraction.Auto) {
        startSpeechRecognizer()
      } else if (voiceInteraction === VoiceInteraction.Manual) {
        changeStatus(VoiceStatus.WaitListen)
      }

      // 自动模式语音，识别到话，取消倒计时，没识别到话，停止聆听，保存开场白，语音播放；
      addLog({
        level: 'debug',
        message: 'initIntro 延迟开场白',
        data: {
          introduction: chatData?.eman.introduction,
          introductionType,
          introductionDelay,
        },
      })
      helloTimer.current = setTimeout(
        async () => {
          addLog({
            level: 'trace',
            message: '延迟开场白结束',
          })
          changeStatus(VoiceStatus.Canceled)
          stopSpeechRecognizer()
          saveIntro()

          helloTimer.current = undefined
        },
        Number(introductionDelay) * 1000,
      )
    } else {
      if (voiceInteraction === VoiceInteraction.Auto) {
        startSpeechRecognizer()
      } else {
        changeStatus(VoiceStatus.WaitListen)
      }
    }
  })

  const initData = useEvent(async () => {
    console.log('initData', {
      chatData,
      emanType,
      scriptType,
      voiceInteraction,
      isShuziren,
    })
    if (!chatData) return
    // 请求e人数据

    console.log('initData audioInstance', audioInstance.current)
    initSpeechRecognizer()
    try {
      // 普通e人情况下，有倒计时、脚本
      if (emanType === EmanType.NORMAL) {
        console.log('普通e人')

        // 倒计时

        timeRef.current.time = chatData.script.timeLimit

        timeRef.current.createTime = chatData.createTime
        const { data: serverTimeData } = await getServerTime()
        timeRef.current.serverTime = serverTimeData.data

        const leftTimeCal = caculateLeftTime()
        setLeftTime(leftTimeCal)

        if (chatData.done) {
          // 对话已完成情况
          addLog({
            level: 'trace',
            message: '对话已完成',
          })
          timeRef.current.isTimeout = true

          finishVoice()
          // 显示关闭弹窗
          setChatActionSheetType(ChatActionSheetType.COMPLETE)
          setShowActionSheet(true)
        } else if (leftTimeCal <= 0) {
          // 倒计时结束但未完成对话，结束对话
          addLog({
            level: 'trace',
            message: '倒计时结束了',
          })
          timeRef.current.isTimeout = true
          cancelServerTime()
          await doneChat(chatId, false)
          doneChatRef.current = true
          finishVoice()
          setChatActionSheetType(ChatActionSheetType.OFF)
          setShowActionSheet(true)
        } else {
          // 还有剩余时间
          addLog({
            level: 'trace',
            message: '还有剩余时间',
          })
          syncTime()

          // 开始音频
          if (scriptType === ScriptType.SKILL) {
            // 技巧类
            initIntro()
            setNavigationBarTitle({
              title: chatData.eman.name,
            })
          } else if (scriptType === ScriptType.QUESTION) {
            // 问答
            // 问答类判断答到第几题了
            const { data: historyRes } = await getAllChatHistory(chatId)
            historyRef.current = historyRes.data
            console.log('historyRes', historyRes)

            questions.current = chatData.qaList

            setQuestionList(chatData.qaList)
            if (historyRes.data.length === 0) {
              saveQuestion(0)
            }
            // 找到哪题是没回答的
            const current = chatData.qaList.findIndex(
              (item) =>
                item.answer === null ||
                item.answer === '' ||
                item.answer === undefined,
            )
            addLog({
              level: 'debug',
              message: '找到哪题是没回答的',
              data: {
                current,
              },
            })
            if (current === -1) {
              // 全部答完了
              timeRef.current.isOver = true
              cancelServerTime()
              changeStatus(VoiceStatus.Complete)
              setQuestionNow(chatData.qaList.length - 1)
            } else {
              const nowQIndex = current
              setQuestionNow(nowQIndex)
              setNavigationBarTitle({
                title: `第${nowQIndex + 1}/${chatData.qaList.length}题`,
              })
              changeStatus(VoiceStatus.Waiting)
              const nextQuestion = questions.current[nowQIndex]
              speakStart()
              speakProcess(nextQuestion.question) // 传给语音合成
            }
          }
        }
      }

      if (emanType === EmanType.INTELLIGENCE) {
        console.log('智能e人')
        setNavigationBarTitle({
          title: chatData.eman.name,
        })
        cancelServerTime()

        try {
          const resStatus = await eManStatus(chatData.eman.id)
          // 判断数字人是否有效
          if (resStatus.data.data) {
            // 有效则判断是否有数字人
            initIntro()
          } else {
            addLog({
              level: 'trace',
              message: '该E人已下架',
            })
            showToast({
              title: '该E人已下架',
              icon: 'none',
            })
          }
        } catch (error) {
          setConnecting(ConnectStatus.CONNECTING)
        }
      }
    } catch (error) {
      console.log('error', error)
      cancelServerTime()
      finishVoice()
    }
  })
  const progressInit = useEvent(() => {
    progressInterval.current = setInterval(() => {
      console.log('progressInterval', connectingProgress)
      setConnectingProgress((p) => {
        if (p < 99) {
          return p + 1
        } else {
          return 99
        }
      })
    }, 50)
  })

  const initPage = useEvent(async () => {
    progressInit()
    eventEmitterRef.current = EventEmitter.getInstance()

    eventEmitterRef.current.on('audioData', async (data: any) => {
      console.log(data, 'eventEmitterRef')
      await instancePictureHuman.current?.audioRender(data)
    })
    try {
      feedbackData.current.chatId = chatId
      feedbackData.current.appId = appId
      const pages = getCurrentPages()
      console.log('pages', pages)
      feedbackData.current.path = window.location.hash
      const { data: chatRes } = await getChat(chatId)
      const chatResData = chatRes.data
      setChatData(chatResData)
      setEmanType(chatResData.eman.type)
      // const { data: emanRes } = await getEnam(chatResData.eman.id)
      // const emanResData = emanRes.data
      // setEmanType(emanResData.type)

      const model = chatResData.eman.zipFileUrl

      if (model) {
        setIsShuziren(true)
      } else {
        setIsShuziren(false)
      }

      if (chatResData.eman.tone) {
        const parseTone = JSON.parse(chatResData.eman.tone)
        console.log('parTone', parseTone)
        addLog({
          level: 'debug',
          message: '设置音调',
          data: {
            tone: parseTone,
          },
        })
        if (parseTone.speed !== null && parseTone.speed !== undefined) {
          tone.current.speed = parseTone.speed
        }
        if (parseTone.volume !== null && parseTone.volume !== undefined) {
          tone.current.volume = parseTone.volume
        }
        if (parseTone.voiceType !== null && parseTone.voiceType !== undefined) {
          tone.current.voiceType = parseTone.voiceType
        }
        if (parseTone.name !== null && parseTone.name !== undefined) {
          tone.current.name = parseTone.name
        }
      }
      let scriptResData
      if (chatResData.eman.type === EmanType.NORMAL) {
        const scriptRes = await getScript(chatResData.script.id)
        scriptResData = scriptRes.data.data
        setScriptType(scriptResData.type)
      }
      // 确定语音交互类型
      const voice_interaction_type =
        StorageUtils.get(
          chatResData.eman.type === EmanType.NORMAL
            ? scriptResData.type === ScriptType.QUESTION
              ? StorageEnvKey.QUESTION_INTERACTION_TYPE
              : StorageEnvKey.SKILL_INTERACTION_TYPE
            : StorageEnvKey.INTELLIGENCE_INTERACTION_TYPE,
        ) || VoiceInteraction.Manual
      console.log(
        'voice_interaction_type',
        voice_interaction_type,
        StorageUtils.get(StorageEnvKey.QUESTION_INTERACTION_TYPE),
        StorageUtils.get(StorageEnvKey.SKILL_INTERACTION_TYPE),
        StorageUtils.get(StorageEnvKey.INTELLIGENCE_INTERACTION_TYPE),
      )
      addLog({
        level: 'debug',
        message: '语音交互类型',
        data: {
          voice_interaction_type,
        },
      })
      setVoiceInteraction(voice_interaction_type)
      const subtitle_visible =
        StorageUtils.get(StorageEnvKey.SUBTITLE_VISIBLE) || SubtitleVisible.Hide
      setSubtitleShow(subtitle_visible)

      // audioInstance.current = new Audio()
      if (model) {
        //  判断是否有数字人，有数字人模式，读取本地的数字人模式，如果开启或没存就初始化，如果没开启就初始化
        let scm = StorageUtils.get(StorageEnvKey.CHAT_MODE)
        addLog({
          level: 'debug',
          message: '数字人模式',
          data: {
            scm,
          },
        })
        if (scm === null || scm === ChatMode.SHUZIREN) {
          // 如果没有存储，或者存储的是数字人模式，则初始化数字人
          changeChatMode(ChatMode.SHUZIREN)
        } else {
          changeChatMode(ChatMode.AUDIO)
        }

        try {
          console.log('开始初始化数字人')
          await initPictureHuman(model)
          if (
            (chatResData.eman.type === EmanType.NORMAL &&
              scriptResData.type === ScriptType.SKILL) ||
            chatResData.eman.type === EmanType.INTELLIGENCE
          ) {
            if (introductionType === '1' || introductionType === '2') {
              textToSpeechIntroduce(chatResData.eman.introduction, 'mp3')
              textToSpeechIntroduce(chatResData.eman.introduction, 'pcm')
            }
          }
        } catch (error) {
          clearInterval(progressInterval.current)
          setConnectingProgress(100)
          if (scm === null || scm === ChatMode.SHUZIREN) {
            // 如果数字人模式，初始化失败，则删掉语音
            showToast({
              title: '数字人初始化失败',
              icon: 'error',
            })
            changeChatMode(ChatMode.AUDIO)
          }
        } finally {
          setConnecting(ConnectStatus.CONNECTED)
        }
      } else {
        // 如果没有数字人，则直接语音
        setIsShuziren(false)
        setConnectingProgress(100)
        setConnecting(ConnectStatus.CONNECTED)
        changeChatMode(ChatMode.AUDIO)
      }
    } catch (error) {}
  })

  // const onActionAudioClose = () => {
  //   setShowAudioOpen(false)
  // }
  // // 打开声音触摸确认
  // const onActionAudioConfirm = () => {
  //   setShowAudioOpen(false)
  //   initData()
  // }
  useReady(() => {
    console.log('Page loaded.')
    initPage()
  })
  useUnload(() => {
    destoryLottieAnimate()
  })

  return (
    <>
      <View className="page">
        <View
          className="bg_box"
          style={{ zIndex: connecting === ConnectStatus.START ? 0 : 2 }}
        >
          <Image
            src={chatData?.eman?.background ?? ''}
            mode="aspectFit"
            className="bg_img"
          />
          <View className="bg_blur" />
        </View>

        <canvas
          id="canvas-pic"
          style={{
            visibility: chatMode === ChatMode.AUDIO ? 'hidden' : 'visible',
          }}
        ></canvas>

        <View className={classNames('content', 'content_bg')}>
          {/* 连接中 */}
          {connecting !== ConnectStatus.START && (
            <View className="connecting_avatar_box">
              <View className="connecting_loading_outer"></View>
              <View className="connecting_loading_inner"></View>
              <Image
                src={chatData?.eman?.avatar ?? ''}
                mode="aspectFit"
                className={classNames('avatar_img', 'avatar_img_active_b')}
              />
            </View>
          )}

          {connecting !== ConnectStatus.START && (
            <View className="connecting_loading_box">
              <View className="connecting_text">
                {connectingProgress === 100 ? '连接成功' : '连接中...'}
              </View>
              <View
                className={classNames('connecting_record_auth', {
                  connecting_record_auth_show: !hasAudioAuth,
                })}
              >
                请打开麦克风进行对话
              </View>
              <View className="connecting_progressbox">
                <View
                  className="connecting_progressbar"
                  style={{ width: `${connectingProgress}%` }}
                ></View>
                <Text className="connecting_percent">
                  {connectingProgress}%
                </Text>
              </View>
            </View>
          )}

          {connecting !== ConnectStatus.START &&
            (connectingProgress === 100 ? (
              <View className="connecting_start_talk" onClick={checkAudioAuth}>
                开始对话
              </View>
            ) : (
              <View className="action_finish" onClick={handleFinish}>
                <Image src={IconHangup} className="action_finish_icon" />
              </View>
            ))}

          {connecting === ConnectStatus.START && (
            <View
              className={classNames('emaninfo_box', {
                emaninfo_box_hide: chatMode === ChatMode.SHUZIREN,
              })}
            >
              {chatMode === ChatMode.AUDIO && (
                <View className="avatar_box">
                  <View
                    className={classNames('wave', {
                      wave_active: status === VoiceStatus.Speaking,
                    })}
                  >
                    <LottieView
                      ref={(animation) => {
                        waveAnimate.current = animation
                      }}
                      style={{
                        width: '100%',
                        height: '100%',
                      }}
                      autoPlay={true}
                      loop={true}
                      source={LottieWave}
                    />
                  </View>

                  <Image
                    src={chatData?.eman?.avatar ?? AvatarDefault}
                    mode="aspectFill"
                    className={classNames('avatar_img', {
                      avatar_img_active:
                        status === VoiceStatus.Waiting ||
                        status === VoiceStatus.Loading,
                      avatar_img_active_b: status === VoiceStatus.Speaking,
                    })}
                  />
                  <View
                    className={classNames('bubble', {
                      bubble_active:
                        status === VoiceStatus.Waiting ||
                        status === VoiceStatus.Loading,
                    })}
                  >
                    <LottieView
                      ref={(animation) => {
                        bubbleAnimate.current = animation
                      }}
                      style={{
                        width: '100%',
                        height: '100%',
                      }}
                      autoPlay={true}
                      loop={true}
                      source={LottieBubble}
                    />
                  </View>
                </View>
              )}
              {chatMode === ChatMode.AUDIO && (
                <View className="scene">{chatData?.scene.scene}</View>
              )}
            </View>
          )}

          {connecting === ConnectStatus.START && (
            <View className="status_box" onClick={statusTextClick}>
              {/* {((emanType === EmanType.NORMAL &&
                scriptType === ScriptType.SKILL) ||
                emanType === EmanType.INTELLIGENCE) &&
              subtitleShow === SubtitleVisible.Show &&
              status === VoiceStatus.Speaking ? (
                <View className="subtitle_box">{subtitleText}</View>
              ) : (
                <Block> */}
              <View className="animation">{statusLoading}</View>
              <View
                className={
                  status === VoiceStatus.Speaking ||
                  status === VoiceStatus.Cancel
                    ? 'status_lite'
                    : 'status'
                }
              >
                {statusText?.map((item) => (
                  <View key={item} className="status_text">
                    {item}
                    {voiceInteraction === VoiceInteraction.Auto &&
                    status === VoiceStatus.Listening
                      ? recognizerTime + 's'
                      : ''}
                  </View>
                ))}
              </View>
              {/* </Block>
              )} */}
            </View>
          )}
          {connecting === ConnectStatus.START && (
            <View className="footer" id="actionfooter">
              {/* 普通e人模式显示时间 */}
              {emanType === EmanType.NORMAL && (
                <View className="time">{timeLeftText}</View>
              )}

              <View className="action_box">
                <View className="action_btn" onClick={handleTextChat}>
                  <Image
                    src={
                      status === VoiceStatus.BeforeListen ||
                      status === VoiceStatus.Listening ||
                      status === VoiceStatus.Overtime
                        ? IconChatDisabled
                        : IconChat
                    }
                    className="action_icon"
                  />
                </View>

                <View className="action_btn" onClick={handleVideoChat}>
                  <Image
                    src={
                      isShuziren && !isSpeaking
                        ? chatMode === ChatMode.AUDIO
                          ? IconVideoClose
                          : IconVideo
                        : IconVideoDisabled
                    }
                    className="action_icon"
                  />
                </View>
                {/* {((emanType === EmanType.NORMAL &&
                  scriptType === ScriptType.SKILL) ||
                  emanType === EmanType.INTELLIGENCE) && (
                  <View className="action_btn" onClick={toggleSubtitle}>
                    <Image
                      src={
                        subtitleShow === SubtitleVisible.Show
                          ? IconSubtitleOn
                          : IconSubtitleOff
                      }
                      className="action_icon"
                    />
                  </View>
                )} */}
                <View className="action_btn" onClick={showMore}>
                  <Image src={IconMore} className="action_icon" />
                </View>
              </View>

              {status === VoiceStatus.Complete ? (
                <View className="action_say_box">
                  <View
                    className={classNames(
                      'action_say',
                      'action_say_active',
                      'action_say_complete',
                    )}
                    onClick={handleComplete}
                  >
                    完成
                  </View>
                </View>
              ) : voiceInteraction === VoiceInteraction.Auto ? (
                <View className="action_finish" onClick={handleFinish}>
                  <Image src={IconHangup} className="action_finish_icon" />
                </View>
              ) : (
                <View className="action_say_box">
                  <Image
                    src={IconHangupMini}
                    className="action_finish_icon_mini"
                    onClick={handleFinish}
                  />
                  <View
                    className={classNames('action_say', {
                      // action_say_disabled: status === VoiceStatus.Speaking,
                      action_say_active:
                        status === VoiceStatus.WaitListen ||
                        status === VoiceStatus.Speaking,
                      action_say_pressed:
                        sayPressed || status === VoiceStatus.Listening,
                      action_say_cancel: status === VoiceStatus.Cancel,
                    })}
                    onTouchStart={handleTouchStart}
                    onTouchMove={handleTouchMove}
                    onTouchEnd={handleTouchEnd}
                  >
                    <View
                      style={{
                        backgroundImage: `url(${
                          status === VoiceStatus.Start ||
                          status === VoiceStatus.Waiting
                            ? IconSpeakDisabled
                            : IconSpeak
                        })`,
                      }}
                    />
                    按住说话
                  </View>
                </View>
              )}
            </View>
          )}
          <View className="tip">对话为情景模拟，非真实场景</View>
        </View>
      </View>
      {/* 开启声音 */}
      {/* <ActionAudio
        show={showAudioOpen}
        onClose={onActionAudioClose}
        onConfirm={onActionAudioConfirm}
      /> */}
      {/* 结束弹窗 */}
      <ActionSheet
        show={showActionSheet}
        zIndex={110}
        onClose={() => setShowActionSheet(false)}
        title={actionSheet?.title}
        className={classNames({
          actionSheet_hide_close: actionSheet?.closeAction,
        })}
        closeOnClickOverlay={false}
      >
        <View className="actionSheet_group">
          {(actionSheet?.actions ?? [])
            .filter((action) => {
              if (action.noChatHistoryHide) {
                return history.length
              } else {
                return true
              }
            })
            .map((action) => {
              return (
                // eslint-disable-next-line taro/no-spread-in-props
                <Button block round {...action.buttonProps} key={action.text}>
                  {action.text}
                </Button>
              )
            })}
        </View>
      </ActionSheet>
      <ScriptActionSheet
        showScript={showScript}
        currentScript={chatData?.script}
        emanName={chatData?.eman.name}
        showScriptClose={() => setShowScript(false)}
      />
      <ActionMore
        show={showMoreActionSheet}
        showCleanContext={emanType === EmanType.INTELLIGENCE}
        onClose={() => setShowMoreActionSheet(false)}
        onScript={() => {
          setShowMoreActionSheet(false)
          setShowScript(true)
        }}
        onSetting={showActionSheetVoiceSetting}
        onCleanContext={() => {
          setShowMoreActionSheet(false)
          setClearShow(true)
        }}
        onFeedback={handleFeedback}
      />
      <ActionVoice
        show={showVoiceActionSheet}
        type={
          emanType === EmanType.NORMAL
            ? scriptType === ScriptType.QUESTION
              ? 'QUESTION_INTERACTION_TYPE'
              : 'SKILL_INTERACTION_TYPE'
            : 'INTELLIGENCE_INTERACTION_TYPE'
        }
        onClose={() => setShowVoiceActionSheet(false)}
        onConfirm={voicePopupConfirm}
      />
      <DialogClearContext
        show={clearShow}
        onClose={() => setClearShow(false)}
        onConfirm={clearContextSend}
      />
      <FeedbackDialog
        show={feedbackShow}
        onClose={() => setFeedbackShow(false)}
        onConfirm={submitFeedback}
      />
      <FeedbackSucess
        show={feedbackSuccessShow}
        onClose={() => setFeedbackSuccessShow(false)}
      />
      <Dialog_ />
    </>
  )
}
