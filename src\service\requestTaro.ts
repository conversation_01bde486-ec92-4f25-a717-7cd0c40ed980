
import config from '@/config';
import StorageEnvKey  from '@/constants/StorageEnvKey';
import frontLogout from '@/utils/fontLogout';
import StorageUtils from '@/utils/StorageUtils';
import Taro from '@tarojs/taro';
import { isEmpty } from 'lodash';

import wx from 'weixin-js-sdk';

const request = ({
    url,
    method = 'GET',
    data,
    showError = true,
    signal,
    timeout = 10000
}: {
    url: string;
    method?: keyof Taro.request.Method | undefined;
    data?: any;
    showError?: boolean;
    reLogin?: boolean;
    signal?: AbortSignal;
    timeout?: number;
}) => {
    return new Promise((resolve, reject) => {
        const header: any = {
            biz: config.biz,
            'Content-Type': 'application/json;charset=utf-8'
        };
        const tenantId = StorageUtils.get(StorageEnvKey.TENANT_ID);

        if (tenantId) {
            header.tenantId = tenantId;
        }

        const isEnterprise = StorageUtils.get(StorageEnvKey.IS_ENTERPRISE);
        if (isEnterprise === '0' || isEnterprise === 0) {
            header.appid = StorageUtils.get(StorageEnvKey.APP_ID);
        }

        const token = StorageUtils.get(StorageEnvKey.TOKEN);

        if (!isEmpty(token)) {
            header.Authorization = token;
        }
        Taro.request({
            url: config.server + process.env.TARO_APP_BASE_API + url,
            method,
            data,
            timeout,
            header,
            signal,
            success:  (res) => {
                console.log('request success',url, data, header, res);
                const { data: resData } = res;
                if (resData.code === 401 || resData.code === 20004) {
                    if (showError) {
                        Taro.showToast({
                            icon: 'none',
                            title: '登录信息失效，请重新登录',
                            mask: true
                        });
                    }
                    frontLogout();
                    setTimeout(
                       () => {
                        wx.miniProgram.redirectTo({
                          url: '/pages/login/index/index'
                        });
                      },
                      showError ? 1500 : 0
                  );

                    reject(res);
                } else if (resData.code === 200) {
                    resolve(res);
                } else {
                    if (showError) {
                        // Taro.showToast({
                        //     icon: 'none',
                        //     title: resData.message
                        // });
                    }

                    reject(res);
                }
            },
            fail: (error) => {
                console.log('request fail',url, data, header, error);
                if (showError) {
                    Taro.showToast({
                        icon: 'none',
                        title: '网络错误'
                    });
                }
                reject(error);
            }
        });
    });
};

export default request;
