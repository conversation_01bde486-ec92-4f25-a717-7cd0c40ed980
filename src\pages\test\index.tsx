
import PictureHuman from '@bddh/starling-dh-picture-client'
import config from '@/config'
import './index.less'
import Taro, {  useLoad, useReady } from "@tarojs/taro";
import { useEffect, useRef } from "react";

import { Button } from '@antmjs/vantui';

export default function Index() {
  const instancePictureHuman = useRef<any>()

  const init = () => {
    try {
      instancePictureHuman.current = new PictureHuman({
        modelUrl: 'https://facefusion-2021-peoplesdaily.bj.bcebos.com/track/picturefigure/people_9sWYt7wmjuS7AKzRM3DLaa_v1_shoubai.zip?version=2',
        licenseUrl: config.shuziren.LICENSE_URL,
        licenseKey: config.shuziren.LICENSE_KEY,

        statusCallback: (res: any) => {
          console.info('PictureHuman', res)
          if (res.code === 0) {
            if (res.status.progress == 100) {
              // 加载完成

            }
          } else {
            // 失败
            console.error('PictureHuman init error', res)

          }
        },
        canvasId: '#canvas-pic',
      })
    } catch (error) {
      console.error('PictureHuman init error', error)
    }
  }

  async function fetchData(url: string): Promise<ArrayBuffer> {
    const response = await Taro.request({
        url,
        method: 'GET',
        responseType: 'arraybuffer' // 设置响应类型为 arraybuffer
    });

    return response.data;
};

  const playAudio = async () => {
    console.log('playAudio')
    const testUrl2 = 'https://star-light-lite.bj.bcebos.com/yinbintest/16k.pcm';
    const buffer = await fetchData(testUrl2);
    instancePictureHuman.current?.audioRender({
      audioBuffer: buffer,
      last: true,
      first: true,
      sample: 16000,
      interrupted: true,
      finishListener: (err)=>{
        if(err){
          console.log('播放失败：',err);
        }else {
          console.log('播放成功');
          
        }
      },
    })
  }

   useEffect(() => {
    navigator.mediaDevices
    .getUserMedia({ video: false, audio: true })
    .then((stream) => {
      try {
        window.localStream = stream // A
        window.localAudio.srcObject = stream // B
        window.localAudio.autoplay = true // C
      } catch (error) {}

      console.log(`you got a stream: ${stream}`)

    })
    .catch((err) => {
      console.error(`you got an error: ${err}`)

    })
    init()
   },[])

   useLoad(() => {
     console.log('useLoad')
     // 加载完成


   })


   useReady(() => {
    console.log('useReady')
    // 加载完成


   })

  return <div className="page">
    <div style={{position: 'absolute',top: 0,left: 0,width: '100%',height: 200,zIndex: 99999}} >
      <button>纯按钮非播放音频</button>
      <button  onClick={playAudio}>播放音频</button>
    </div>
  <canvas id="canvas-pic" canvas-id="canvas-pic" type="2d" />
  </div>
}
