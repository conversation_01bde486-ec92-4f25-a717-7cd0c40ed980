@import '@/styles/function.less';
img {
  -webkit-touch-callout: none;
}
.page {
  width: 100vw;
  height: 100vh;
  position: relative;
}
.bg {
  &_box {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 0;
    width: 100%;
    height: 100%;
  }
  &_img {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 0;
    width: 100%;
    height: 100%;
  }
  &_blur {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(80px);
  }
}
#canvas-pic {
  width: 100%;
  height: 100%;
  position: absolute;
  z-index: 1;
  object-fit: cover;
  /* transform: scale(1.25); */
  /* transform-origin: 50% 50%; */
}
.time {
  padding-top: 30px;
  margin-bottom: 24px;
  color: #fff;
  font-weight: 400;
  font-size: 28px;
  text-align: center;
}
.content {
  position: relative;
  z-index: 2;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  align-items: center;

  &_bg {
    background: linear-gradient(0deg, #000000 0%, rgba(0, 0, 0, 0) 50%);
  }
}

.connecting {
  &_avatar_box {
    position: relative;
    margin-top: 200px;
  }
  &_loading {
    &_outer {
      position: absolute;
      top: 0;
      left: 0;
      z-index: 0;
      width: 320px;
      height: 320px;
      border-radius: 100%;
      background-color: rgba(255, 255, 255, 0.05);
      animation: connecting-loading-outer 1.5s infinite linear;

    }
    &_inner {
      position: absolute;
      top: 0;
      left: 0;
      z-index: 0;
      width: 320px;
      height: 320px;
      border-radius: 100%;
      background-color: rgba(255, 255, 255, 0.05);
      animation: connecting-loading-inner 1.5s infinite linear;
    }
  }
  &_loading_box {
    flex: 1;
    margin-bottom: 120px;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-end;
  }
  &_text {
    font-size: 32px;
    text-align: center;
    color: #fff;
    font-weight: bold;
  }
  &_progressbox {
    width: 80%;
    padding: 6px;
    box-sizing: border-box;
    border-radius: 66px;
    border: 1px solid #6298FF;
    position: relative;
  }
  &_record_auth {
    text-align: center;
    color: rgba(255, 255, 255, 0.5);
    font-size: 24px;
    height: 44px;
    padding-top: 16px;
    visibility: hidden;
    &_show {
      visibility: visible;
    }
  }
  &_progressbar {
    width: 0;
    height: 28px;
    border-radius: 56px;
    background: linear-gradient(270deg, #6741FE 0%, #9EC1FF 100%);
  }
  &_percent {
    font-size: 24px;
    line-height: 1;
    color: white;
    position: absolute;
    right: 14px;
    top: 50%;
    transform: translateY(-50%);
  }

  &_start_talk {
    width: 70%;
    height: 90px;
    border-radius: 90px;
    font-size: 28px;
    margin-bottom: 24px;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
    background: linear-gradient(270deg, #6742FF 0%, #3D83FF 100%);
  }
}

@keyframes connecting-loading-outer {
  0% {
    transform: scale(1);
    opacity: 0;
  }
  80% {
    transform: scale(1.4);
    opacity: 1;
  }

  100% {
    transform: scale(1);
    opacity: 0;
  }
}

@keyframes connecting-loading-inner {
  0% {
    transform: scale(1);
    opacity: 0;
  }
  50% {
    transform: scale(1.2);
    opacity: 1;
  }

  100% {
    transform: scale(1);
    opacity: 0;
  }
}

.emaninfo_box {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;

  &_hide {
    visibility: hidden;
  }
}


.avatar {
  &_box {
      position: relative;
      margin-top: 200px;
  }
  &_img {
      position: relative;
      z-index: 1;
      width: 300px;
      height: 300px;
      border-width: 10px;
      border-style: solid;
      border-color: rgba(255, 255, 255, 0);
      border-radius: 100%;
      transition: all 0.1s ease-in-out;
      &_active {
          border-color: #fff;
          box-shadow: 0 0 14px 8px rgba(255, 255, 255, 0.8);
      }
      &_active_b {
          border: 10px solid #fff;
      }
  }
}


.scene {
  padding-top: 60px;
  margin-right: 82px;
  margin-left: 81px;
  color: rgba(255, 255, 255, 0.4);
  font-size: 28px;
  text-align: center;
}
.status_box {
  width: 100%;
  height: 300px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

}
.subtitle_box {
  background: rgba(0, 0, 0, 0.5);
  border-radius: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  font-size: 28px;
  padding: 16px 32px;
  color: white;
  width: 80%;
  box-sizing: border-box;
  min-height: 120px;
  max-height: 240px;
  flex-shrink: 0;
  flex-grow: 0;
}
.animation {
  margin-bottom: 20px;
}
.bubble {
  position: absolute;
  top: -85px;
  right: -168px;
  z-index: 2;
  width: 336px;
  height: 179px;
  display: none;

  &_active {
      display: block;
  }
}
.wave {
  position: absolute;
  top: -43px;
  left: -242px;
  z-index: 0;
  width: 800px;
  height: 400px;
  display: none;

  &_active {
      display: block;

  }
}

.status {
  height: 110px;
  color: #fff;
  font-weight: 600;
  font-size: 36px;
  text-align: center;

  &_lite {
      height: 110px;
      color: rgba(255, 255, 255, 0.5);
      font-weight: 400;
      font-size: 24px;
      text-align: center;
  }

  &_text {
      line-height: 50px;
  }
}
.footer {
  width: 100%;
}

.action {
  &_box {
    display: flex;
    gap: 48px;
    justify-content: center;
    margin-bottom: 22px;
  }
  &_btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 104px;
    height: 104px;
    border-radius: 100%;
    background: rgba(0, 0, 0, 0.2);
  }
  &_finish {
    width: 112px;
    height: 112px;
    border-radius: 112px;
    margin: 0 auto;

    &_icon {
      width: 100%;
      height: 100%;
    }
  }

  &_icon {
    width: 48px;
    height: 48px;
  }

}
.action_say_box {
  display: flex;
  gap: 20px;
  position: relative;
  z-index: 1;
  padding-left: 48px;
  padding-right: 48px;
  padding-top: 22px;
}
.action_finish_icon_mini {
  width: 130px;
  height: 90px;
}
.action_say {
  height: 90px;
  width: 0;
  position: relative;
  flex: 1;
  border-radius: 90px;
  font-size: 28px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.2);
  color: rgba(243, 243, 243, 0.5);

  &_disabled {
      background-color: rgba(0, 0, 0, 0.2);
      color: rgba(243, 243, 243, 0.5);
  }
  &_active {
      color: #fff;
      background: linear-gradient(270deg, #6742FF 0%, #3D83FF 100%);


  }
  &_pressed {
      color: #fff;
      background: linear-gradient(270deg, #5237CC 0%, #3269CC 100%);

      &::after {
          content: '';
          position: absolute;
          z-index: 0;
          top: -10px;
          left: -3%;
          width: 106%;
          height:110px;
          background: linear-gradient(270deg,rgba(103, 66, 255, 0.3) 0%, rgba(61, 131, 255, 0.3) 100%);
          border-radius: 130px;
      }
  }
  &_cancel {
      background: #ff4545;
      color: #fff;

      &::after {
          display: none;
        }

  }
  &_icon_speak {
    width: 48px;
    height: 48px;
    margin-right: 8px;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    background-position: center;
  }
}


.tip {
  margin-top: 32px;
  margin-bottom: 56px;
  color: rgba(255, 255, 255, 0.5);
  font-size: 24px;
  text-align: center;
}

.actionSheet {
  &_group {
    .wrap-box(12px 62px 54px 62px);
    display: flex;
    flex-direction: column;
    gap: 24px;
  }
  &_hide_close {
    --action-sheet-header-height: 124px;
    .van-action-sheet__close {
      display: none !important;
    }
  }
}

.cancel_icon {
  width: 56px;
  height: 56px;
}
.complete_icon {
  width: 88px;
  height: 88px;
}
