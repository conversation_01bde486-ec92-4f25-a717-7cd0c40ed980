
interface Events {
  [key: string]: Function[];
}

export default class EventEmitter {
  private static instance: EventEmitter;
  private events: Events = {};

  private constructor() { }

  static getInstance(): EventEmitter {
      if (!EventEmitter.instance) {
          EventEmitter.instance = new EventEmitter();
      }
      return EventEmitter.instance;
  }

  on(eventName: string, cb: Function): void {
      if (!this.events[eventName]) {
          this.events[eventName] = [];
      }
      this.events[eventName].push(cb);
  }

  emit(eventName: string, ...args: any[]): void {
      if (!this.events[eventName]) {
          console.error(`No listeners set for event: ${eventName}`);
          return;
      }
      this.events[eventName].forEach((cb: Function) => {
          cb(...args);
      });
  }

  off(eventName: string, cb: Function): void {
      if (!this.events[eventName]) {
          console.error(`No listeners set for event: ${eventName}`);
          return;
      }
      this.events[eventName] = this.events[eventName].filter((listener: Function) => listener !== cb);
  }
}
