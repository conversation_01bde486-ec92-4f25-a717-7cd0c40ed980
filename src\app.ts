import { PropsWithChildren } from 'react'
import { useLaunch, useRouter } from '@tarojs/taro'
import VConsole from '@/utils/vconsole.min.js'
import { isEmpty } from 'lodash'
import './app.less'

import StorageEnvKey from './constants/StorageEnvKey'
import StorageUtils  from './utils/StorageUtils'


function App({ children }: PropsWithChildren<any>) {
  const {params} = useRouter<{token:string;tenantId:string;appId:string;isEnterprise:string,debug:string}>()
  console.log(params, 'app')
  let {token, tenantId, appId, isEnterprise} = params
  if(!isEmpty(token)) {
    StorageUtils.set(StorageEnvKey.TOKEN, token);
  }
  if(!isEmpty(tenantId)) {
    StorageUtils.set(StorageEnvKey.TENANT_ID, tenantId);
  }

  StorageUtils.set(StorageEnvKey.APP_ID, appId);
  StorageUtils.set(StorageEnvKey.IS_ENTERPRISE, isEnterprise);

  useLaunch(() => {
    console.log('App launched.')
    if(process.env.TARO_APP_MODE === 'develop' || params.debug === 'true') {
       const vconsole = new VConsole()
       console.log('vconsole', vconsole)
    }

  })

  // children 是将要会渲染的页面
  return children
}

export default App
